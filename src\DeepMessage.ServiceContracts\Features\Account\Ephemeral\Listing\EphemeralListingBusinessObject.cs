﻿using DeepMessage.Framework.Core;
namespace DeepMessage.ServiceContracts.Features.Account;

/// <summary>
/// Listing business object for AES key management queries
/// Used for retrieving AES key information for friendships
/// </summary>
public class EphemeralListingBusinessObject
{
    /// <summary>
    /// The friendship ID
    /// </summary>
    public string FriendshipId { get; set; } = null!;

    /// <summary>
    /// AES-256 key encrypted with sender's RSA public key
    /// Base64-encoded encrypted key data
    /// </summary>
    public string? SenderAESKey { get; set; }

    /// <summary>
    /// Same AES-256 key encrypted with receiver's RSA public key
    /// Base64-encoded encrypted key data
    /// </summary>
    public string? ReceiverAESKey { get; set; }

    /// <summary>
    /// Whether the AES keys are currently valid and active
    /// </summary>
    public bool AESKeyActive { get; set; }

    /// <summary>
    /// Timestamp when AES keys were created for rotation tracking
    /// </summary>
    public DateTime? AESKeyCreatedAt { get; set; }

    /// <summary>
    /// User ID of the friendship owner (for filtering)
    /// </summary>
    public string UserId { get; set; } = null!;

    /// <summary>
    /// Friend ID in the friendship relationship
    /// </summary>
    public string FriendId { get; set; } = null!;
}
