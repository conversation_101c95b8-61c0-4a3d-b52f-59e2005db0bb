﻿using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.Cient.Common.Data;
using System.Security.Claims;
using DeepMessage.Client.Common.Data;
using Platform.Framework.Core;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Text;

namespace Platform.Client.Services.Features.Conversation;
public class ChatMessagesClientSideListingDataService : ClientSideListingDataService<ChatMessagesListingBusinessObject, ChatMessagesFilterBusinessObject>, IChatMessagesListingDataService
{
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly IClientEncryptionService encryptionService;
    private readonly ISecureKeyManager secureKeyManager;
    private readonly IAESKeyManagerService aesKeyManagerService;

    public ChatMessagesClientSideListingDataService(AppDbContext context, ILocalStorageService localStorageService,
        IClientEncryptionService encryptionService, ISecureKeyManager secureKeyManager, IAESKeyManagerService aesKeyManagerService)
    {
        this.context = context;
        this.localStorageService = localStorageService;
        this.encryptionService = encryptionService;
        this.secureKeyManager = secureKeyManager;
        this.aesKeyManagerService = aesKeyManagerService;
    }
    public override IQueryable<ChatMessagesListingBusinessObject> GetQuery(ChatMessagesFilterBusinessObject filterBusinessObject)
    {
        var userId = Task.Run(() => (localStorageService.GetValue(ClaimTypes.NameIdentifier))).Result;
         
        // ✅ FIXED: Query MessageRecipient table for E2E encrypted messages
        var query = from mr in context.MessageRecipients
                    join m in context.Messages on mr.MessageId equals m.Id
                    where m.ConversationId == filterBusinessObject.ConversationId
                          && (mr.RecipientId == userId || m.SenderId == userId)
                    select new ChatMessagesListingBusinessObject
                    {
                        Id = m.Id,
                        Content = mr.EncryptedContent,
                        IsIncoming = m.SenderId != userId,
                        Timestamp = m.CreatedAt,
                        DeliveryStatus = m.DeliveryStatus,
                        SenderId = m.SenderId,
                        ReceiverId = mr.RecipientId
                        //Attachments = m.Attachments.Select(a => new MessageAttachmentInfo
                        //{
                        //    Id = a.Id,
                        //    AttachmentType = a.AttachmentType,
                        //    FileName = a.FileName,
                        //    FileUrl = a.FileUrl,
                        //    FileSizeBytes = a.FileSizeBytes,
                        //    MimeType = a.MimeType,
                        //    ThumbnailBase64 = a.ThumbnailData != null ? Convert.ToBase64String(a.ThumbnailData) : null,
                        //    UploadProgress = 100, // Client-side stored messages are already uploaded
                        //    IsUploading = false
                        //}).ToList()
                    };

        // Note: Search functionality disabled for encrypted content
        // Search would need to be implemented in UI layer after decryption
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            // For E2E encryption, search must be performed in UI layer after decryption
            // Server/service layer cannot search encrypted content
            // This could be enhanced by implementing client-side search after decryption
        }

        // Order by timestamp (oldest first for chat messages)
        return query.OrderByDescending(message => message.Timestamp);
    }

    /// <summary>
    /// Decrypts a message content using AES decryption
    /// This method should be called by the UI layer after retrieving encrypted messages
    /// </summary>
    /// <param name="encryptedContent">Base64-encoded encrypted message content</param>
    /// <param name="senderId">The sender's user ID for AES key lookup</param>
    /// <param name="recipientId">The recipient's user ID for AES key lookup</param>
    /// <returns>Decrypted plaintext message, or error message if decryption fails</returns>
    public async Task<string> DecryptMessageContentAsync(string encryptedContent, string senderId, string recipientId)
    {
        try
        {
            if (string.IsNullOrEmpty(encryptedContent))
                return "[Empty Message]";

            var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
                return "[User Not Authenticated]";

            // Use AES decryption for all messages
            var friendshipId = CombineGuidsXor(senderId, recipientId);
            var isSender = (senderId == userId);

            var aesKey = await aesKeyManagerService.GetAESKeyForDecryptionAsync(friendshipId, isSender);
            if (aesKey == null)
            {
                return "[AES Key Not Available - Unable to decrypt message]";
            }

            return encryptionService.DecryptWithAESAsync(encryptedContent, aesKey);
        }
        catch (Exception ex)
        {
            // Log the error but don't expose sensitive details
            return $"[Decryption Failed: {ex.Message}]";
        }
    }

  
    /// <summary>
    /// Generates a deterministic friendship ID by combining two user IDs
    /// </summary>
    /// <param name="guid1">First user ID</param>
    /// <param name="guid2">Second user ID</param>
    /// <returns>Combined friendship ID</returns>
    private string CombineGuidsXor(string guid1, string guid2)
    {
        string combined = string.Empty;
        for (int i = 0; i < guid1.Length; i++)
        {
            combined += (i % 2 == 0) ? guid1[i] : guid2[i];
        }
        return combined;
    }
}
