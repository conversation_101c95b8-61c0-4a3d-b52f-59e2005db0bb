using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.Client.Common.Data;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using DeepMessage.MauiApp.Services;
using Microsoft.Extensions.Logging;
using Platform.Framework.Core;

namespace Platform.Client.Services.Features.Account;

/// <summary>
/// Offline-first client-side form service for AES key management
/// Stores AES key data in local SQLite database with sync capabilities
/// </summary>
public class EphemeralOfflineFormDataService : IEphemeralFormDataService
{
    private readonly AppDbContext _context;
    private readonly ILocalStorageService _localStorageService;
    private readonly ChatSyncUpService chatSyncUpService;
    private readonly ILogger<EphemeralOfflineFormDataService> _logger;

    public EphemeralOfflineFormDataService(
        AppDbContext context,
        ILocalStorageService localStorageService,
        ChatSyncUpService chatSyncUpService,
        ILogger<EphemeralOfflineFormDataService> logger)
    {
        _context = context;
        _localStorageService = localStorageService;
        this.chatSyncUpService = chatSyncUpService;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(EphemeralFormBusinessObject formBusinessObject)
    { 
        try
        {
            var messages = _context.Friendships.ToList(); 

            // Find the friendship record
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => f.Id == formBusinessObject.FriendshipId);
 
            ArgumentNullException.ThrowIfNull(friendship);

            friendship.SenderAESKey = formBusinessObject.SenderAESKey;
            friendship.ReceiverAESKey = formBusinessObject.ReceiverAESKey;
            friendship.AESKeyCreatedAt = DateTime.UtcNow;
            friendship.AESKeyActive = true;
            friendship.SyncStatus = 1;
             
            await _context.SaveChangesAsync();
            chatSyncUpService.Sync(new ChatSyncItem()
            {
                Id = friendship.Id,
                SyncType = SyncType.Ephemeral,
            });

            _logger.LogInformation("AES keys {Operation} for friendship {FriendshipId} by user  ",
                formBusinessObject.Operation, formBusinessObject.FriendshipId);

            return friendship.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving AES keys for friendship {FriendshipId} (offline)", formBusinessObject.FriendshipId);
            throw;
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<EphemeralFormBusinessObject> GetItemByIdAsync(string id)
    {
        var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);

        ArgumentNullException.ThrowIfNullOrEmpty(id);

        var friendship = await _context.Friendships
            .FirstOrDefaultAsync(f => f.Id == id);
         

        return new EphemeralFormBusinessObject
        {
            FriendshipId = friendship?.Id,
            SenderAESKey = friendship?.SenderAESKey,
            ReceiverAESKey = friendship?.ReceiverAESKey,
            Operation = ""
        };
    }
}
