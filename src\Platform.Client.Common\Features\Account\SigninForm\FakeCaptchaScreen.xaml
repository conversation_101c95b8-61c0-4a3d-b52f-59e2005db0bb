<?xml version="1.0" encoding="utf-8" ?>
<local:FakeCaptchaScreenBase
    x:Class="Platform.Client.Common.Features.Account.FakeCaptchaScreen"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Account"
    Title="Activate your account"
    x:DataType="local:FakeCaptchaScreen"
    Background="{StaticResource OverlayColor}"
    IsBusy="True">
    <Grid Padding="16" RowDefinitions="1*, Auto, 2*">
        <Border
            x:Name="MainBorder"
            Grid.Row="1"
            Background="{StaticResource CardBackgroundColor}"
            StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="8" />
            </Border.StrokeShape>

            <ScrollView>
                <Grid RowDefinitions="Auto, Auto, Auto, Auto, Auto, Auto">

                    <!--  Header Section  -->
                    <VerticalStackLayout Margin="16,32,16,0" Spacing="8">
                        <!--  Conditional display based on registration status  -->
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="20"
                            HorizontalTextAlignment="Center"
                            Text="{Binding HeaderMessage}"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />

                        <!--  Activation status or instructions  -->
                        <Label
                            HorizontalTextAlignment="Center"
                            Text="{Binding SubHeaderMessage}"
                            TextColor="{AppThemeBinding Light={StaticResource Gray400},
                                                        Dark={StaticResource Gray300}}" />
                    </VerticalStackLayout>

                    <!--  Reset Registration Option (shown when user is registered)  -->
                    <HorizontalStackLayout
                        Grid.Row="1"
                        Margin="16,0"
                        HorizontalOptions="Center"
                        IsVisible="{Binding IsUserRegistered}">
                        <Label
                            Grid.Column="0"
                            FontSize="12"
                            Text="{Binding ObfuscatedUsername, StringFormat='Activated for {0}'}"
                            TextColor="{StaticResource Secondary600}"
                            VerticalOptions="Center" />
                        <Button
                            Grid.Column="1"
                            BackgroundColor="Transparent"
                            Command="{Binding ResetRegistrationCommand}"
                            FontSize="12"
                            IsEnabled="True"
                            Text="Reset"
                            TextColor="{StaticResource Blue600}" />
                    </HorizontalStackLayout>

                    <!--  Captcha Section (shown when user is registered)  -->
                    <VerticalStackLayout
                        Grid.Row="2"
                        Margin="16,0,0,8"
                        IsVisible="{Binding IsUserRegistered}"
                        Spacing="8">
                        <!--  Captcha Image Placeholder  -->
                        <Border
                            Background="{StaticResource SurfaceColor}"
                            HeightRequest="64"
                            Stroke="{StaticResource BorderColor}"
                            StrokeThickness="2"
                            WidthRequest="192">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="4" />
                            </Border.StrokeShape>
                            <Image
                                HorizontalOptions="Center"
                                Source="{Binding CaptchaImage}"
                                VerticalOptions="Center" />
                        </Border>

                        <!--  Captcha Controls  -->
                        <HorizontalStackLayout
                            Margin="16,0"
                            HorizontalOptions="Center"
                            Spacing="16">
                            <Button
                                Background="Transparent"
                                Command="{Binding RefreshCaptchaCommand}"
                                FontSize="10"
                                HeightRequest="16"
                                TextColor="{StaticResource Blue600}">
                                <Button.ImageSource>
                                    <FontImageSource
                                        FontFamily="Jelly"
                                        Glyph="&#xf021;"
                                        Size="16"
                                        Color="{StaticResource Blue600}" />
                                </Button.ImageSource>
                                <Button.Text>Refresh</Button.Text>
                            </Button>
                            <Button
                                Background="Transparent"
                                Command="{Binding AudioCaptchaCommand}"
                                FontSize="10"
                                HeightRequest="16"
                                TextColor="{StaticResource Blue600}">
                                <Button.ImageSource>
                                    <FontImageSource
                                        FontFamily="Jelly"
                                        Glyph="&#xf04b;"
                                        Size="16"
                                        Color="{StaticResource Blue600}" />
                                </Button.ImageSource>
                                <Button.Text>Audio</Button.Text>
                            </Button>
                        </HorizontalStackLayout>
                    </VerticalStackLayout>

                    <Label
                        Grid.Row="3"
                        Margin="16,16"
                        IsVisible="{Binding IsUserRegistered, Converter={StaticResource InvertedBoolConverter}}"
                        Text="You can obtain purchase code from brieflynews.com/purchase or get a refferal code from a friend." />

                    <!--  Enhanced Error Display  -->
                    <Border
                        Grid.Row="4"
                        Margin="16,0"
                        Background="#FEF2F2"
                        IsVisible="{Binding HasError}"
                        Stroke="#FECACA"
                        StrokeThickness="1">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="8" />
                        </Border.StrokeShape>
                        <Grid Padding="12" ColumnDefinitions="Auto,*">
                            <Image
                                Grid.Column="0"
                                Margin="0,2,8,0"
                                HeightRequest="20"
                                Source="error_icon.svg"
                                VerticalOptions="Start"
                                WidthRequest="20" />
                            <VerticalStackLayout Grid.Column="1" Spacing="4">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="Verification failed"
                                    TextColor="#DC2626" />
                                <Label
                                    FontSize="12"
                                    Text="{Binding Error}"
                                    TextColor="#991B1B" />
                            </VerticalStackLayout>
                        </Grid>
                    </Border>

                    <!--  Auth Code Form  -->
                    <VerticalStackLayout
                        Grid.Row="5"
                        Margin="16,0"
                        Spacing="16">

                        <!--  Auth Code Field  -->
                        <VerticalStackLayout Margin="0,8,0,0" Spacing="8">
                            <Label
                                FontAttributes="Bold"
                                FontFamily="MulishExtraBold"
                                FontSize="14"
                                Text="{Binding AuthCodeLabel}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray400},
                                                            Dark={StaticResource Gray300}}" />
                            <Grid>
                                <Image
                                    HorizontalOptions="End"
                                    Source="lock_keyhole_solid.svg"
                                    WidthRequest="24" />
                                <Entry
                                    x:Name="entryAuthCode"
                                    Grid.Column="1"
                                    Background="Transparent"
                                    FontSize="16"
                                    IsPassword="True"
                                    Placeholder="{Binding AuthCodePlaceholder}"
                                    PlaceholderColor="{AppThemeBinding Light={StaticResource Gray300},
                                                                       Dark={StaticResource Gray700}}"
                                    Text="{Binding SelectedItem.PassKey}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />
                            </Grid>
                        </VerticalStackLayout>

                        <!--  Enhanced Submit Button  -->
                        <Button
                            Margin="40,8"
                            Command="{Binding SaveCommand}"
                            Text="Next" />


                        <!--  Back to Search Link  -->
                        <HorizontalStackLayout
                            Margin="0,8"
                            HorizontalOptions="Center"
                            Spacing="5">
                            <Label
                                FontFamily="Poppins"
                                FontSize="14"
                                Text="Already activated an account?"
                                TextColor="{AppThemeBinding Light={StaticResource Gray400},
                                                            Dark={StaticResource Gray300}}" />
                            <Label
                                x:Name="ClickHereLabel"
                                FontAttributes="Bold"
                                FontFamily="Poppins"
                                FontSize="14"
                                Text="Click here"
                                TextColor="{StaticResource Blue600}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding GoToLoginCommand}" />
                                </Label.GestureRecognizers>
                            </Label>
                        </HorizontalStackLayout>
                    </VerticalStackLayout>


                    <Button
                        Margin="8,0"
                        BackgroundColor="Transparent"
                        Clicked="Button_Clicked"
                        CornerRadius="0"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        WidthRequest="24">
                        <Button.ImageSource>
                            <FontImageSource
                                FontFamily="Jelly"
                                Glyph="&#xf057;"
                                Size="20"
                                Color="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />
                        </Button.ImageSource>
                    </Button>
                </Grid>
            </ScrollView>
        </Border>

        <Grid
            Grid.RowSpan="3"
            Background="{StaticResource OverlayColor}"
            IsVisible="{Binding IsWorking}">

            <Border
                Padding="30"
                Background="{StaticResource LoadingBackgroundColor}"
                HorizontalOptions="Center"
                Stroke="{StaticResource Gray500Brush}"
                VerticalOptions="Center">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="16" />
                </Border.StrokeShape>
                <Border.Shadow>
                    <Shadow
                        Brush="Gray"
                        Opacity="0.2"
                        Radius="10"
                        Offset="0,4" />
                </Border.Shadow>
                <VerticalStackLayout HorizontalOptions="Center" Spacing="15">
                    <ActivityIndicator IsRunning="True" Color="{StaticResource Secondary700}" />
                    <Label
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalTextAlignment="Center"
                        Text="Working, please wait...!"
                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                </VerticalStackLayout>
            </Border>
        </Grid>


    </Grid>
</local:FakeCaptchaScreenBase>