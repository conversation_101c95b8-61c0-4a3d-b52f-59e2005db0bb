using DeepMessage.Client.Common.Data;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Platform.Framework.Core;
using System.Security.Claims;

namespace Platform.Client.Services;

/// <summary>
/// Service that automatically syncs missing conversation and friendship data
/// when messages are received from unknown senders
/// </summary>
public class AutoSyncService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<AutoSyncService> _logger;

    public AutoSyncService(IServiceScopeFactory scopeFactory, ILogger<AutoSyncService> logger)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
    }

    /// <summary>
    /// Ensures that conversation and friendship data exists for a given message
    /// If missing, automatically syncs the required data from server
    /// </summary>
    /// <param name="conversationId">The conversation ID from the incoming message</param>
    /// <param name="senderId">The sender ID from the incoming message</param>
    /// <returns>True if all required data is available, false otherwise</returns>
    public async Task<bool> EnsureRequiredDataExists(string conversationId, string senderId)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("User ID not found in local storage");
                return false;
            }

            // Check if conversation exists locally
            var conversationExists = await context.Conversations
                .AnyAsync(c => c.Id == conversationId);

            // Check if conversation participant records exist
            var participantExists = await context.ConversationParticipants
                .AnyAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId);

            // Check if friendship data exists for the sender
            var friendshipExists = await context.Friendships
                .AnyAsync(f => f.UserId == userId && f.FriendId == senderId);

            // If any required data is missing, sync it
            if (!conversationExists || !participantExists)
            {
                _logger.LogInformation("Missing conversation data for {ConversationId}, syncing from server", conversationId);
                await SyncConversationData(conversationId);
            }

            if (!friendshipExists)
            {
                _logger.LogInformation("Missing friendship data for sender {SenderId}, syncing from server", senderId);
                await SyncFriendshipData(senderId);
                PubSub.Hub.Default.Publish("FriendDataUpdated");
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring required data exists for conversation {ConversationId} and sender {SenderId}", 
                conversationId, senderId);
            return false;
        }
    }

    /// <summary>
    /// Syncs conversation data for a specific conversation ID from server
    /// </summary>
    private async Task SyncConversationData(string conversationId)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var chatThreadSyncService = scope.ServiceProvider.GetRequiredService<IChatThreadSyncFormDataService>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);

            // ✅ FRAMEWORK PATTERN: Use existing GetItemByIdAsync method from base interface
            var targetConversation = await chatThreadSyncService.GetItemByIdAsync(conversationId);

            if (targetConversation != null)
            {
                // Check if conversation already exists locally
                var existingConversation = await context.Conversations.FirstOrDefaultAsync(c => c.Id == conversationId);
                if (existingConversation == null)
                {
                    // Create new conversation record
                    var conversation = new Conversation
                    {
                        Id = targetConversation.Id,
                        CreatedAt = targetConversation.CreatedAt,
                        IsDeleted = targetConversation.IsDeleted,
                        Title = targetConversation.Title,
                        Type = targetConversation.Type,
                        SyncStatus = 1
                    };
                    context.Conversations.Add(conversation);
                }

                // Sync conversation participants
                foreach (var participant in targetConversation.ChatParticipents)
                {
                    var existingParticipant = await context.ConversationParticipants
                        .FirstOrDefaultAsync(cp => cp.Id == participant.Id);

                    if (existingParticipant == null)
                    {
                        var conversationParticipant = new ConversationParticipant
                        {
                            Id = participant.Id,
                            ConversationId = participant.ConversationId,
                            UserId = participant.UserId,
                            IsAdmin = participant.IsAdmin,
                            JoinedAt = participant.JoinedAt
                        };
                        context.ConversationParticipants.Add(conversationParticipant);
                    }
                }

                await context.SaveChangesAsync();
                _logger.LogDebug("Successfully synced conversation data for {ConversationId}", conversationId);
            }
            else
            {
                _logger.LogWarning("Conversation {ConversationId} not found on server", conversationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing conversation data for {ConversationId}", conversationId);
            throw;
        }
    }

    /// <summary>
    /// Syncs friendship data for a specific friend ID from server
    /// </summary>
    private async Task SyncFriendshipData(string friendId)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var friendsClientService = scope.ServiceProvider.GetRequiredKeyedService<IFriendsListingDataService>("client");
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("User ID not found in local storage during friendship sync");
                return;
            }

            _logger.LogDebug("Starting friendship sync for UserId={UserId}, FriendId={FriendId}", userId, friendId);

            // ✅ FRAMEWORK PATTERN: Use SearchKey to filter by friend ID
            var filterBusinessObject = new FriendsFilterBusinessObject
            {
                SearchKey = friendId, // Pass friend ID as SearchKey for GUID-based filtering
                RowsPerPage = 1 // Only need one result
            };

            _logger.LogDebug("Attempting to sync friendship data for friend ID: {FriendId}", friendId);
            var pagedItems = await friendsClientService.GetPaginatedItems(filterBusinessObject);
            var targetFriend = pagedItems?.Items?.FirstOrDefault();

            _logger.LogDebug("Friend lookup result: Found={Found}, TotalItems={TotalItems}",
                targetFriend != null, pagedItems?.Items?.Count ?? 0);

            if (targetFriend != null)
            {
                _logger.LogDebug("Found friend data: Id={Id}, FriendId={FriendId}, Name={Name}",
                    targetFriend.Id, targetFriend.FriendId, targetFriend.Name);
                // ✅ FIXED: Check if friendship already exists by UserId and FriendId combination
                var existingFriendship = await context.Friendships
                    .FirstOrDefaultAsync(f => f.UserId == userId && f.FriendId == targetFriend.FriendId);
                _logger.LogDebug("Existing friendship check: Found={Found} for UserId={UserId} and FriendId={FriendId}",
                    existingFriendship != null, userId, targetFriend.FriendId);

                if (existingFriendship == null)
                {
                    var friendship = new Friendship
                    {
                        Id = targetFriend.Id,
                        UserId = userId,
                        FriendId = targetFriend.FriendId,
                        Name = targetFriend.Name,
                        AvatarData = targetFriend.AvatarData,
                        TagLine = targetFriend.TagLine,
                        Pub1 = targetFriend.Pub1,
                        CreatedAt = DateTime.UtcNow,
                        SyncStatus = 1, // Mark as synced
                        IsBlocked = false,
                        IsDeleted = false
                    };
                    context.Friendships.Add(friendship);
                    _logger.LogDebug("Added new friendship: Id={Id}, UserId={UserId}, FriendId={FriendId}, Name={Name}",
                        friendship.Id, friendship.UserId, friendship.FriendId, friendship.Name);
                }
                else
                {
                    // Update existing friendship details
                    existingFriendship.Name = targetFriend.Name;
                    existingFriendship.AvatarData = targetFriend.AvatarData;
                    existingFriendship.TagLine = targetFriend.TagLine;
                    existingFriendship.Pub1 = targetFriend.Pub1;
                    existingFriendship.SyncStatus = 1; // Mark as synced
                    _logger.LogDebug("Updated existing friendship: Id={Id}, Name={Name}",
                        existingFriendship.Id, existingFriendship.Name);
                }

                var changesSaved = await context.SaveChangesAsync();
                _logger.LogDebug("Successfully synced friendship data for {FriendId}, changes saved: {ChangesSaved}",
                    friendId, changesSaved);
            }
            else
            {
                _logger.LogWarning("Friend {FriendId} not found on server", friendId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing friendship data for {FriendId}", friendId);
            throw;
        }
    }
}
