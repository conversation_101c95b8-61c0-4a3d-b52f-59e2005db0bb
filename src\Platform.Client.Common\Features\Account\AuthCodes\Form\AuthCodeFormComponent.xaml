﻿<?xml version="1.0" encoding="utf-8" ?>
<local:AuthCodeFormViewBase
    x:Class="Platform.Client.Common.Features.AuthCodes.AuthCodeFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.AuthCodes"
    Title="Generate Authentication Code"
    x:DataType="local:AuthCodeFormView"
    Background="{StaticResource OverlayColor}"
    IsBusy="True">

    <!--  Modal Overlay Layout  -->
    <Grid Padding="16" RowDefinitions="1*, Auto, 2*">

        <!--  Main Content Container  -->
        <ScrollView Grid.Row="1">
            <Border
                x:Name="MainBorder"
                Padding="0"
                Background="{StaticResource CardBackgroundColor}"
                Opacity="0"
                Scale="0.9"
                StrokeThickness="0">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="12" />
                </Border.StrokeShape>

                <Grid RowDefinitions="Auto,Auto,Auto,Auto,Auto,8" RowSpacing="16">


                    <!--  Header with Close Button  -->
                    <VerticalStackLayout
                        Grid.Column="0"
                        Margin="16,24,16,8"
                        Spacing="4">
                        <Label
                            FontAttributes="Bold"
                            FontSize="20"
                            Text="Generate Authentication Code"
                            TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                        Dark={StaticResource Gray200}}" />
                        <Label
                            FontSize="14"
                            LineHeight="1.4"
                            Text="Create a secure authentication code to share with friends. It allows them to add you to their contacts."
                            TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                        Dark={StaticResource Gray400}}" />
                    </VerticalStackLayout>

                    <!--  How it works  -->
                    <Border
                        Grid.Row="1"
                        Margin="16,0"
                        Padding="16"
                        Background="{AppThemeBinding Light={StaticResource Secondary50},
                                                     Dark={StaticResource Gray900}}"
                        StrokeThickness="0">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="12" />
                        </Border.StrokeShape>

                        <VerticalStackLayout Grid.Column="1" Spacing="4">
                            <Label
                                FontAttributes="Bold"
                                FontSize="14"
                                Text="How Authentication Codes Work"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                            <Label
                                FontSize="12"
                                LineHeight="1.4"
                                Text="• Codes expire after 5 minutes for security&#x0a;• Each code can only be used once&#x0a;• Share only with people you trust"
                                TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                            Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>

                    </Border>

                    <!--  Generated Code Display Area  -->
                    <Border
                        Grid.Row="2"
                        Margin="16,4,16,0"
                        Padding="0"
                        Background="{AppThemeBinding Light={StaticResource BostonBlue50},
                                                     Dark={StaticResource Gray800}}"
                        IsVisible="{Binding SelectedItem.AuthCode, Converter={StaticResource StringToBooleanConverter}}"
                        StrokeThickness="0.1">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="12" />
                        </Border.StrokeShape>

                        <Grid RowDefinitions="Auto,Auto,Auto" RowSpacing="12">
                            <!--  Header  -->
                            <Border
                                Grid.Row="0"
                                Padding="16,12"
                                Background="{AppThemeBinding Light={StaticResource BostonBlue50},
                                                             Dark={StaticResource BostonBlue900}}"
                                StrokeThickness="0">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="12,12,0,0" />
                                </Border.StrokeShape>

                                <Grid ColumnDefinitions="Auto,*" ColumnSpacing="8">
                                    <Label
                                        Grid.Column="0"
                                        FontFamily="Jelly"
                                        FontSize="16"
                                        Text="&#xf00c;"
                                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                    Dark={StaticResource Gray400}}"
                                        VerticalOptions="Center" />
                                    <Label
                                        Grid.Column="1"
                                        FontAttributes="Bold"
                                        FontSize="14"
                                        Text="Authentication Code Generated Successfully"
                                        TextColor="{AppThemeBinding Light={StaticResource BostonBlue700},
                                                                    Dark={StaticResource BostonBlue300}}"
                                        VerticalOptions="Center" />
                                </Grid>
                            </Border>

                            <!--  Code Display  -->
                            <HorizontalStackLayout Grid.Row="1">
                                <Label
                                    Margin="16"
                                    FontFamily="Jelly"
                                    FontSize="18"
                                    HorizontalOptions="Center"
                                    Text="&#xf023;"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                                Dark={StaticResource Gray400}}"
                                    VerticalOptions="Center" />

                                <VerticalStackLayout Grid.Column="1" VerticalOptions="Center">
                                    <Label
                                        FontSize="12"
                                        Text="Your Authentication Code"
                                        TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                                    Dark={StaticResource Gray400}}" />
                                    <Label
                                        FontAttributes="Bold"
                                        FontFamily="Courier"
                                        FontSize="28"
                                        Text="{Binding SelectedItem.AuthCode}"
                                        TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                                    Dark={StaticResource Gray200}}" />
                                </VerticalStackLayout>
                            </HorizontalStackLayout>

                            <!--  Action Buttons  -->
                            <Grid
                                Grid.Row="2"
                                Padding="16,8,16,16"
                                ColumnDefinitions="*,12,*"
                                ColumnSpacing="0">

                                <!--  Copy Button  -->
                                <Button
                                    Grid.Column="0"
                                    Padding="12"
                                    Background="{AppThemeBinding Light={StaticResource Gray500},
                                                                 Dark={StaticResource Gray600}}"
                                    Clicked="OnCopyClicked"
                                    CornerRadius="8"
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="Copy"
                                    TextColor="White">
                                    <Button.ImageSource>
                                        <FontImageSource
                                            FontFamily="Jelly"
                                            Glyph="&#xf24d;"
                                            Size="20"
                                            Color="White" />
                                    </Button.ImageSource>
                                </Button>

                                <!--  Share Button  -->
                                <Button
                                    Grid.Column="2"
                                    Padding="12"
                                    Background="{AppThemeBinding Light={StaticResource BostonBlue500},
                                                                 Dark={StaticResource BostonBlue600}}"
                                    Clicked="OnShareClicked"
                                    CornerRadius="8"
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="Share"
                                    TextColor="White">
                                    <Button.ImageSource>
                                        <FontImageSource
                                            FontFamily="Jelly"
                                            Glyph="&#xf1e0;"
                                            Size="20"
                                            Color="White" />
                                    </Button.ImageSource>
                                </Button>
                            </Grid>
                        </Grid>
                    </Border>


                    <!--  Generate Button (Centered)  -->
                    <Button
                        Grid.Row="4"
                        Margin="40,0"
                        Command="{Binding SaveCommand}"
                        Text="Generate New Code" />

                    <Button
                        Margin="12,4"
                        BackgroundColor="Transparent"
                        Clicked="OnCloseClicked"
                        CornerRadius="0"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        WidthRequest="24">
                        <Button.ImageSource>
                            <FontImageSource
                                FontFamily="Jelly"
                                Glyph="&#xf057;"
                                Size="20"
                                Color="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />
                        </Button.ImageSource>
                    </Button>

                </Grid>
            </Border>
        </ScrollView>
    </Grid>
</local:AuthCodeFormViewBase>
