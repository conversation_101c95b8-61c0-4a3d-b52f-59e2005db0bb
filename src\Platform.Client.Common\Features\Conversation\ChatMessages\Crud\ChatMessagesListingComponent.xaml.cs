﻿using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Framework.Core;
using DeepMessage.MauiApp.Services;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Logging;
using MobileApp.MauiShared;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Windows.Input;
using DeepMessage.Client.Common.Data;
using Microsoft.EntityFrameworkCore;
using Platform.Framework.Core;
namespace Platform.Client.Common.Features.Conversation;
public class ChatMessagesListingViewBase : CrudBaseMaui<IChatItem, ChatMessagesListingBusinessObject,
                                           ChatMessagesFilterViewModel, ChatMessagesFilterBusinessObject, IChatMessagesListingDataService,
                                           ChatMessageFormBusinessObject, ChatMessageFormViewModel, string, IChatMessageFormDataService>
{
    public ChatMessagesListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}

public partial class ChatMessagesListingView : ChatMessagesListingViewBase
{
    private readonly string conversationId;
    private readonly ISecureKeyManager secureKeyManager;
    private readonly MessageDecryptionService messageDecryptionService;
    private readonly ILocalStorageService localStorageService;
    private readonly AppDbContext dbContext;
    private readonly IServiceScope _serviceScope;
    private readonly ILogger<ChatMessagesListingView>? _logger;
    private RSA? rsaKey;
    private volatile bool _isDisposed = false;

    public ICommand BackCommand => new Command(async () => await Navigation.PopAsync());

    public override int RowsPerPage { get => 5; }
    public override bool LoadItemsOnEveryAppear => false;
    bool firstTime = true;
    public string FriendName { get; }
    public string FriendAvatar { get; }
    private CancellationTokenSource? _hideCts;


    public ChatMessagesListingView(IServiceScopeFactory scopeFactory, string conversationId,
        string friendName, string friendAvatar) : base(scopeFactory)
    {
        this.conversationId = conversationId;
        FriendName = friendName;
        FriendAvatar = friendAvatar;

        // Create a properly managed service scope
        _serviceScope = scopeFactory.CreateScope();
        secureKeyManager = _serviceScope.ServiceProvider.GetRequiredService<ISecureKeyManager>();
        messageDecryptionService = _serviceScope.ServiceProvider.GetRequiredService<MessageDecryptionService>();
        localStorageService = _serviceScope.ServiceProvider.GetRequiredService<ILocalStorageService>();
        dbContext = _serviceScope.ServiceProvider.GetRequiredService<AppDbContext>();

        // Get logger for debugging (optional)
        _logger = _serviceScope.ServiceProvider.GetService<ILogger<ChatMessagesListingView>>();

        InitializeComponent();
        BindingContext = this;
        FilterViewModel.ConversationId = conversationId;
    }

    protected override IChatItem[] ConvertListingBusinessItemsToListingViewModelItems(List<ChatMessagesListingBusinessObject> listBusinessObjects)
    {
        var orderedItems = new List<IChatItem>();
        DateTime lastDate = DateTime.MinValue;
        foreach (var item in listBusinessObjects.OrderBy(x => x.Timestamp))
        {
            if (lastDate.Date != item.Timestamp.GetValueOrDefault().Date)
            {
                orderedItems.Add(new DateSeparatorItem(item.Timestamp.GetValueOrDefault()));
                lastDate = item.Timestamp.GetValueOrDefault();
            }
       
            orderedItems.Add(new ChatMessagesListingViewModel()
            {
                Id = item.Id,
                Content = DecryptMessageContentAsync(item.Content!, item.IsIncoming, item.SenderId!, item.ReceiverId!),
                IsIncoming = item.IsIncoming,
                DeliveryStatus = item.DeliveryStatus,
                Timestamp = item.Timestamp
            });
        }

        //Parallel.ForEach(listBusinessObjects.OrderBy(x => x.Timestamp), async (item, i, n) =>
        //{

        //    if (lastDate.Date != item.Timestamp.GetValueOrDefault().Date)
        //    {
        //        orderedItems[n] = new DateSeparatorItem(item.Timestamp.GetValueOrDefault());
        //        lastDate = item.Timestamp.GetValueOrDefault();
        //    }
        //    var decryptedContent =   DecryptMessageContentAsync(item.Content!, item.IsIncoming, item.SenderId!, item.ReceiverId!);
        //    orderedItems[n] = new ChatMessagesListingViewModel()
        //    {
        //        Id = item.Id,
        //        Content = decryptedContent,
        //        IsIncoming = item.IsIncoming,
        //        DeliveryStatus = item.DeliveryStatus,
        //        Timestamp = item.Timestamp,
        //    };

        //});


        return orderedItems.ToArray();
    }



    /// <summary>
    /// Async helper method for AES-based message decryption
    /// </summary>
    private  string  DecryptMessageContentAsync(string encryptedContent, bool isIncoming, string senderId, string receiverId)
    {
        // Get current user ID
        //var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
        //if (string.IsNullOrEmpty(userId))
        //{
        //    return "[User Not Authenticated]";
        //}

        // Get the friend ID from conversation participants
        //var friendId = await GetFriendIdFromConversationAsync(conversationId, userId);
        //if (string.IsNullOrEmpty(friendId))
        //{
        //    return "[Unable to determine conversation participant]";
        //}

        // Generate friendship ID for AES key lookup
        var friendshipId = messageDecryptionService.GenerateFriendshipId(senderId, receiverId);

        // Determine if current user is sender or receiver based on message direction
        var isCurrentUserSender = !isIncoming;

        // Try decryption with the determined role
        var result = messageDecryptionService.DecryptMessageAsync(encryptedContent, friendshipId, isCurrentUserSender);

        if (result.Success)
        {
            return result.Content;
        }

        // Return appropriate error message based on error type
        return result.ErrorType switch
        {
            DecryptionErrorType.UserNotAuthenticated => "[User Not Authenticated]",
            DecryptionErrorType.KeyNotFound => "[AES Key Not Available - Unable to decrypt message]",
            DecryptionErrorType.DecryptionFailed => "[Message Decryption Failed]",
            DecryptionErrorType.NetworkError => "[Network Error - Unable to retrieve keys]",
            _ => $"[Decryption Failed: {result.ErrorMessage}]"
        };
    }

    /// <summary>
    /// Gets the friend ID from conversation participants
    /// </summary>
    private async Task<string?> GetFriendIdFromConversationAsync(string conversationId, string userId)
    {
        try
        {
            var friendId = await (from cp in dbContext.ConversationParticipants
                                  where cp.ConversationId == conversationId && cp.UserId != userId
                                  select cp.UserId).FirstOrDefaultAsync();

            return friendId;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting friend ID from conversation {ConversationId}", conversationId);
            return null;
        }
    }

    /// <summary>
    /// Generates a deterministic friendship ID by combining two user IDs
    /// </summary>
    private string CombineGuidsXor(string guid1, string guid2)
    {
        if (string.IsNullOrEmpty(guid1) || string.IsNullOrEmpty(guid2))
        {
            throw new ArgumentException("Both GUIDs must be non-null and non-empty");
        }

        if (guid1.Length != guid2.Length)
        {
            throw new ArgumentException("Both GUIDs must have the same length");
        }

        string combined = string.Empty;
        for (int i = 0; i < guid1.Length; i++)
        {
            combined += (i % 2 == 0) ? guid1[i] : guid2[i];
        }

        return combined;
    }

    protected override void OnAppearing()
    {
        Shell.SetTabBarIsVisible(this, false);
        var signalRClientService = ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<SignalRClientService>();
        if (firstTime)
        {
            firstTime = false;
            PubSub.Hub.Default.Subscribe<string>((m) =>
            {
                if (m == "NewMessageReceived")
                {
                    // ✅ FIXED: Handle new message notification with proper threading
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await LoadItems(false);
                            _logger?.LogDebug("Reloaded items after NewMessageReceived for conversation {ConversationId}", conversationId);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Failed to reload items after NewMessageReceived for conversation {ConversationId}", conversationId);
                        }
                    });
                }
            });
            WeakReferenceMessenger.Default.Register<ChatMessageStatus>(this, (r, m) =>
            {
                var item = Items.FirstOrDefault(x => x.Id == m.Id);
                if (item is ChatMessagesListingViewModel chatMessage && chatMessage.DeliveryStatus != m.DeliveryStatus)
                {
                    chatMessage.DeliveryStatus = m.DeliveryStatus;
                    chatMessage.Timestamp = m.Timestamp;

                    //todo:update db for read status instead, may be via queue?  await signalRClientService.AcknowledgeMessageRead(chatMessage.Id, "", DateTime.UtcNow);
                }
            });
        }

        base.OnAppearing();

        // ✅ FIXED: Ensure ScrollTo happens on UI thread
        MainThread.BeginInvokeOnMainThread(() =>
        {
            if (Items.Count > 0)
            {
                try
                {
                    collection.ScrollTo(Items.Count - 1);
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Failed to scroll to last message on appearing");
                }
            }
        });
    }


    /// <summary>
    /// Handles post-save operations with proper thread safety
    /// CRITICAL FIX: Ensures UI updates happen on main thread after message save
    /// </summary>
    public override async Task OnAfterSaveAsync(string key)
    {
        try
        {
            // ✅ FIXED: Clear content on UI thread
            MainThread.BeginInvokeOnMainThread(() =>
            {
                SelectedItem.Content = null;
            });

            // Load items (this will properly handle UI thread via UpdateItems)
            await LoadItems(false);

            // ✅ FIXED: Scroll to bottom after new message is added
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    if (Items.Count > 0)
                    {
                        collection.ScrollTo(Items.Count - 1);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Failed to scroll to last message after save");
                }
            });

            _logger?.LogDebug("Successfully processed OnAfterSaveAsync for conversation {ConversationId}", conversationId);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error in OnAfterSaveAsync for conversation {ConversationId}", conversationId);
        }
    }

    /// <summary>
    /// Updates the Items collection with proper UI thread handling
    /// CRITICAL FIX: Ensures all UI collection operations happen on the main thread
    /// </summary>
    public override void UpdateItems(PagedDataList<ChatMessagesListingBusinessObject> pagedItems,
        IChatItem[] viewModelItems)
    {
        // ✅ FIXED: Check if disposed to prevent race conditions
        if (_isDisposed)
        {
            _logger?.LogWarning("Attempted to update items on disposed ChatMessagesListingView");
            return;
        }

        // ✅ FIXED: Wrap all UI collection operations in MainThread.BeginInvokeOnMainThread
        MainThread.BeginInvokeOnMainThread(() =>
        {
            try
            {
                // Double-check disposal state on UI thread
                if (_isDisposed) return;
                // For chat messages, we want to append new messages rather than replace all
                // This preserves the chat history and provides better UX
                foreach (var item in viewModelItems)
                {
                    if (!Items.Any(x => x.Id == item.Id))
                    {
                        Items.Add(item);
                    }
                    else
                    {
                        // Update existing item if content has changed
                        var existingItem = Items.FirstOrDefault(x => x.Id == item.Id);
                        if (existingItem is ChatMessagesListingViewModel existingMsg &&
                            item is ChatMessagesListingViewModel newMsg &&
                            existingMsg.ContentHash != newMsg.ContentHash)
                        {
                            existingMsg.DeliveryStatus = newMsg.DeliveryStatus;
                            existingMsg.Timestamp = newMsg.Timestamp;
                            existingMsg.Content = newMsg.Content;
                        }
                    }
                }

                // Update pagination info
                TotalPages = pagedItems.TotalPages;
                TotalRows = pagedItems.TotalRows;

                _logger?.LogDebug("Updated {Count} chat messages on UI thread", viewModelItems.Length);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating chat messages collection");
            }
        });
    }

    protected override async Task ItemsLoaded(IChatMessagesListingDataService service)
    {
        collection.ScrollTo(Items.Count - 1);
        var pendingUpdates = Items.Where(x => x is ChatMessagesListingViewModel message
        && message.DeliveryStatus != MessageDeliveryStatus.ReadByEndUser
        && message.IsIncoming);
        if (pendingUpdates.Any())
        {
            var scope = ScopeFactory.CreateScope();
            var signalRClientService = scope.ServiceProvider.GetRequiredService<SignalRClientService>();
            foreach (ChatMessagesListingViewModel item in pendingUpdates)
            {
                try
                {
                    //todo: await signalRClientService.AcknowledgeMessageRead(item.Id, "", DateTime.UtcNow);
                    //item.DeliveryStatus = MessageDeliveryStatus.ReadByEndUser;
                    //_logger?.LogDebug("Acknowledged message read on items loaded: {MessageId}", item.Id);
                }
                catch (Exception ex)
                {
                    // Handle the exception, e.g., log it
                    Console.WriteLine($"Error acknowledging message read: {ex.Message}");
                }
            }
        }
        await base.ItemsLoaded(service);
    }

    protected override Task<ChatMessageFormViewModel> CreateSelectedItem()
    {
        return Task.FromResult(new ChatMessageFormViewModel()
        {
            ConversationId = conversationId
        });
    }

    /// <summary>
    /// Handles CollectionView size changes with proper UI thread safety
    /// </summary>
    private void CollectionView_SizeChanged(object sender, EventArgs e)
    {
        // ✅ FIXED: Ensure ScrollTo happens on UI thread
        MainThread.BeginInvokeOnMainThread(() =>
        {
            try
            {
                if (Items.Count > 0)
                {
                    collection.ScrollTo(Items.Count - 1);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to scroll to last message on size changed");
            }
        });
    }



    void OnCollectionViewScrolled(object sender, ItemsViewScrolledEventArgs e)
    {
        // e.FirstVisibleItemIndex is the flat index in ItemsList
        var vm = BindingContext as ChatMessagesListingView;
        var idx = e.FirstVisibleItemIndex;
        if (vm == null || idx < 0 || idx >= vm.Items.Count)
            return;

        // pick the date to show
        string dateText;
        var item = vm.Items[idx];
        if (item is DateSeparatorItem ds)
            dateText = ds.Date.ToString("dd MMM yyyy");
        else if (item is ChatMessagesListingViewModel msg)
            dateText = msg.Timestamp.GetValueOrDefault().Date.ToString("dd MMM yyyy");
        else
            return;

        lblDateOverlay.Text = dateText;
        borderDateOverlay.IsVisible = true;

        // reset the hide timer
        _hideCts?.Cancel();
        _hideCts = new CancellationTokenSource();
        _ = HideOverlayAfterDelayAsync(_hideCts.Token);
    }

    async Task HideOverlayAfterDelayAsync(CancellationToken token)
    {
        try
        {
            await Task.Delay(1000, token);
            borderDateOverlay.IsVisible = false;
        }
        catch (TaskCanceledException) { }
    }

    /// <summary>
    /// Disposes of resources including RSA key and service scope
    /// Matches disposal pattern from MessagesListing.razor.cs
    /// </summary>
    public void Dispose()
    {
        if (_isDisposed) return;

        try
        {
            // Set disposed flag first to prevent race conditions
            _isDisposed = true;

            // Dispose RSA key
            rsaKey?.Dispose();
            rsaKey = null;

            // Cancel any pending operations
            _hideCts?.Cancel();
            _hideCts?.Dispose();
            _hideCts = null;

            // Dispose service scope
            _serviceScope?.Dispose();

            _logger?.LogDebug("ChatMessagesListingView disposed for conversation {ConversationId}", conversationId);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during disposal of ChatMessagesListingView for conversation {ConversationId}", conversationId);
        }
    }
}

public class ChatTemplateSelector : DataTemplateSelector
{
    public DataTemplate MessageTemplate { get; set; }
    public DataTemplate DateSeparatorTemplate { get; set; }

    protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
    {
        return item switch
        {
            DateSeparatorItem => DateSeparatorTemplate,
            ChatMessagesListingViewModel => MessageTemplate,
            _ => MessageTemplate
        };
    }
}
// To fix the CS0311 error, the type 'IChatItem' must implement 'IEquatable<IChatItem>'.
// Adding the implementation to the 'IChatItem' interface and its relevant classes.



