﻿using Android.Views;
using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiApp.Services;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Platform;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Security.Claims;
using System.Text;
using System.Windows.Input;
using Platform.Framework.Core;
namespace Platform.Client.Common.Features.Conversation;
public class ChatThreadsListingViewBase : ListingBaseMaui<ChatThreadsListingViewModel, ChatThreadsListingBusinessObject,
                                            ChatThreadsFilterViewModel, ChatThreadsFilterBusinessObject, IChatThreadsListingDataService>
{
    public ChatThreadsListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}


public partial class ChatThreadsListingView : ChatThreadsListingViewBase, IDisposable
{
    private readonly ISecureKeyManager secureKeyManager;
    private readonly IAESKeyManagerService aesKeyManagerService;
    private readonly IClientEncryptionService encryptionService;
    private readonly ILocalStorageService localStorageService;
    private readonly AppDbContext dbContext;
    private readonly IServiceScope _serviceScope;
    private readonly ILogger<ChatThreadsListingView>? _logger;
    private RSA? rsaKey;

    public ChatThreadsListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        // Create a properly managed service scope
        _serviceScope = scopeFactory.CreateScope();
        secureKeyManager = _serviceScope.ServiceProvider.GetRequiredService<ISecureKeyManager>();
        aesKeyManagerService = _serviceScope.ServiceProvider.GetRequiredService<IAESKeyManagerService>();
        encryptionService = _serviceScope.ServiceProvider.GetRequiredService<IClientEncryptionService>();
        localStorageService = _serviceScope.ServiceProvider.GetRequiredService<ILocalStorageService>();
        dbContext = _serviceScope.ServiceProvider.GetRequiredService<AppDbContext>();

        // Get logger for debugging (optional)
        _logger = _serviceScope.ServiceProvider.GetService<ILogger<ChatThreadsListingView>>();

        InitializeComponent();

        MessageTappedCommand = new Command<ChatThreadsListingViewModel>(async (p) =>
        { 
            var chat = new ChatMessagesListingView(scopeFactory, p.Id, p.Name, p.Avatar);
            await Navigation.PushAsync(chat, false);
        });

        PubSub.Hub.Default.Subscribe<string>((m) =>
        {
            if (m == "NewMessageReceived")
            { 
                _ = LoadItems(false);
            }
        });

        WeakReferenceMessenger.Default.Register<ChatMessageStatus>(this, (r, m) =>
        {
            _ = LoadItems(false);
        });
        BindingContext = this;
    }

    protected override void OnAppearing()
    { 
        // Initialize RSA key for decryption (matches Razor implementation)
        InitializeRSAKey();

        base.OnAppearing();
    }

    /// <summary>
    /// Initializes the RSA key for message decryption
    /// Matches the pattern from ChatThreadsListing.razor.cs OnInitializedAsync
    /// </summary>
    private void InitializeRSAKey()
    {
        try
        {
            if (secureKeyManager.IsRSAKeyAvailable())
            {
                rsaKey = secureKeyManager.GetRSAPrivateKeyAsync();
                _logger?.LogDebug("RSA key initialized successfully for chat threads listing");
            }
            else
            {
                _logger?.LogWarning("RSA key not available during initialization for chat threads listing");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to initialize RSA key for chat threads listing");
        }
    }

    /// <summary>
    /// Refreshes the RSA key if it becomes unavailable
    /// Useful for handling authentication state changes
    /// </summary>
    private void RefreshRSAKeyIfNeeded()
    {
        try
        {
            if (rsaKey == null && secureKeyManager.IsRSAKeyAvailable())
            {
                rsaKey = secureKeyManager.GetRSAPrivateKeyAsync();
                _logger?.LogDebug("RSA key refreshed for chat threads listing");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to refresh RSA key for chat threads listing");
        }
    }

    /// <summary>
    /// Converts business objects to view models with decrypted last messages
    /// Matches the exact implementation from ChatThreadsListing.razor.cs ConvertToListViewModel
    /// </summary>
    protected override ChatThreadsListingViewModel[] ConvertListingBusinessItemsToListingViewModelItems(List<ChatThreadsListingBusinessObject> listBusinessObjects)
    {
        return listBusinessObjects.Where(x => x != null).Select(x => new ChatThreadsListingViewModel
        {
            Id = x!.Id,
            Avatar = x.Avatar,
            Name = x.Name,
            LastMessage = DecryptLastMessage(x.LastMessage, x.Id), // Pass conversation ID for AES key lookup
            LastMessageTime = x.LastMessageTime
        }).ToArray();
    }

    /// <summary>
    /// Decrypts last message content using AES encryption with ephemeral key system
    /// Follows offline-first pattern with server fallback
    /// </summary>
    private string DecryptLastMessage(string? encryptedContent, string conversationId)
    {
        if (string.IsNullOrEmpty(encryptedContent))
            return string.Empty;

        try
        {
            // Use async helper method for AES decryption
            return Task.Run(() => DecryptLastMessageAsync(encryptedContent, conversationId)).Result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to decrypt last message content for conversation {ConversationId}", conversationId);
            return "[Failed to decrypt message]";
        }
    }

    /// <summary>
    /// Async helper method for AES-based last message decryption
    /// </summary>
    private async Task<string> DecryptLastMessageAsync(string encryptedContent, string conversationId)
    {
        try
        {
            // Get current user ID
            var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return "[User Not Authenticated]";
            }

            // Get the friend ID from conversation participants
            var friendId = await GetFriendIdFromConversationAsync(conversationId, userId);
            if (string.IsNullOrEmpty(friendId))
            {
                return "[Unable to determine conversation participant]";
            }

            // Generate friendship ID for AES key lookup
            var friendshipId = CombineGuidsXor(userId, friendId);

            // For decryption, we need to determine if current user is sender or receiver
            // Since we don't have sender info in the message, we'll try both roles
            var aesKey = await aesKeyManagerService.GetAESKeyForDecryptionAsync(friendshipId, true);
            if (aesKey == null)
            {
                // Try as receiver
                aesKey = await aesKeyManagerService.GetAESKeyForDecryptionAsync(friendshipId, false);
            }

            if (aesKey == null)
            {
                return "[AES Key Not Available - Unable to decrypt message]";
            }

            // Decrypt message content with AES key
            return encryptionService.DecryptWithAESAsync(encryptedContent, aesKey);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error in AES last message decryption for conversation {ConversationId}", conversationId);
            return $"[Decryption Failed: {ex.Message}]";
        }
    }

    /// <summary>
    /// Gets the friend ID from conversation participants
    /// </summary>
    private async Task<string?> GetFriendIdFromConversationAsync(string conversationId, string userId)
    {
        try
        {
            var friendId = await (from cp in dbContext.ConversationParticipants
                                  where cp.ConversationId == conversationId && cp.UserId != userId
                                  select cp.UserId).FirstOrDefaultAsync();

            return friendId;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting friend ID from conversation {ConversationId}", conversationId);
            return null;
        }
    }

    /// <summary>
    /// Generates a deterministic friendship ID by combining two user IDs
    /// </summary>
    private string CombineGuidsXor(string guid1, string guid2)
    {
        if (string.IsNullOrEmpty(guid1) || string.IsNullOrEmpty(guid2))
        {
            throw new ArgumentException("Both GUIDs must be non-null and non-empty");
        }

        if (guid1.Length != guid2.Length)
        {
            throw new ArgumentException("Both GUIDs must have the same length");
        }

        string combined = string.Empty;
        for (int i = 0; i < guid1.Length; i++)
        {
            combined += (i % 2 == 0) ? guid1[i] : guid2[i];
        }

        return combined;
    }

    public ICommand MessageTappedCommand { get; set; }

    private ICommand? _syncDownItemsCommand;
    public ICommand? SyncDownItemsCommand
    {
        get
        {
            return _syncDownItemsCommand = _syncDownItemsCommand ?? new Command(async () =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    IsBusy = true;
                });

                using (var scope = ScopeFactory.CreateScope())
                {
                    Error = string.Empty;
                    try
                    {
                        var listingService = scope.ServiceProvider.GetRequiredService<IChatThreadSyncFormDataService>();
                        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                        var items = await listingService.GetItemsAsync() ?? throw new Exception("Paginated items object is null");
                        foreach (var conversationItem in items)
                        {
                            var conversation = await context.Conversations.FirstOrDefaultAsync(x => x.Id == conversationItem.Id);

                            if (conversation == null)
                            {
                                conversation = new DeepMessage.Client.Common.Data.Conversation()
                                {
                                    Id = conversationItem.Id,
                                    CreatedAt = conversationItem.CreatedAt,
                                    IsDeleted = conversationItem.IsDeleted,
                                    Type = conversationItem.Type,
                                    Title = conversationItem.Title

                                };
                                context.Conversations.Add(conversation);
                                await context.SaveChangesAsync();
                            }

                            ArgumentNullException.ThrowIfNull(conversationItem.ChatParticipents);

                            foreach (var participentItem in conversationItem.ChatParticipents)
                            {
                                var participent = await context.ConversationParticipants.FirstOrDefaultAsync(x => x.Id == participentItem.Id);
                                if (participent == null)
                                {
                                    participent = new ConversationParticipant()
                                    {
                                        Id = participentItem.Id,
                                        ConversationId = participentItem.ConversationId,
                                        IsAdmin = participentItem.IsAdmin,
                                        JoinedAt = participentItem.JoinedAt,
                                        UserId = participentItem.UserId,
                                    };
                                    context.ConversationParticipants.Add(participent);
                                    await context.SaveChangesAsync();
                                }
                            }
                        }

                        await LoadItems(true);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        _ = Shell.Current.GoToAsync("//signin");
                    }

                    catch (Exception ex)
                    {
                        //Crashes.TrackError(ex);
                        if (ex.Message.Contains("invalid_token"))
                        {
                            _ = Shell.Current.GoToAsync("//signin");
                        }
                        Error = ex.Message;
                    }
                }


                MainThread.BeginInvokeOnMainThread(() =>
                {
                    IsBusy = false;
                });
            });
        }
    }

    /// <summary>
    /// Disposes of resources including RSA key and service scope
    /// Matches disposal pattern from ChatMessagesListingComponent and ChatThreadsListing.razor.cs
    /// </summary>
    public void Dispose()
    {
        try
        {
            // Dispose RSA key
            rsaKey?.Dispose();
            rsaKey = null;

            // Dispose service scope
            _serviceScope?.Dispose();

            _logger?.LogDebug("ChatThreadsListingView disposed");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during disposal of ChatThreadsListingView");
        }
    }
}




