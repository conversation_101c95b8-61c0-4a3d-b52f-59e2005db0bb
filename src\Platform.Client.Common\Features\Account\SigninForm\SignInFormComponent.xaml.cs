using System.Windows.Input;

namespace Platform.Client.Common.Features.Account
{
   

    public partial class SignInFormComponent : ContentPage
    {

        // UI State Properties - Focus management removed, using platform defaults

        private bool hasError;
        public bool HasError
        {
            get => hasError;
            set
            {
                hasError = value;
                OnPropertyChanged();
            }
        }
         

        public string ServiceKey => "client";

        public SignInFormComponent(IServiceScopeFactory scopeFactory) 
        {
            InitializeComponent();
             

            BindingContext = SignInFormComponentViewModel.Default;

            // Set initial state for fade-in animation
            this.Opacity = 0;
            this.Scale = 1.05;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();

            // Animate fade-in when the page appears
            await AnimateFadeIn();
        }

        /// <summary>
        /// Animates the fade-in effect when the login screen appears
        /// </summary>
        private async Task AnimateFadeIn()
        {
            try
            {
                // Small delay to ensure the page is fully loaded
                await Task.Delay(150);

                // Create smooth fade in with scale animation
                var fadeTask = this.FadeTo(1, 600, Easing.CubicInOut);
                var scaleTask = this.ScaleTo(1, 600, Easing.CubicInOut);

                // Wait for both animations to complete
                await Task.WhenAll(fadeTask, scaleTask);

                // Add a subtle bounce effect to form elements
               // await AnimateFormElements();
            }
            catch (Exception ex)
            {
                // Fallback to immediate visibility if animation fails
                System.Diagnostics.Debug.WriteLine($"Fade-in animation failed: {ex.Message}");
                this.Opacity = 1;
                this.Scale = 1;
            }
        }

        /// <summary>
        /// Animates form elements with a subtle staggered entrance
        /// </summary>
        private void AnimateFormElements()
        {
            try
            {
                // Find form elements and animate them with a staggered effect
                var formElements = new List<View>();

                // Try to find common form elements by type
                var stackLayout = this.FindByName<VerticalStackLayout>("FormContainer");
                if (stackLayout != null)
                {
                    foreach (View child in stackLayout.Children)
                    {
                        if (child is VerticalStackLayout || child is Entry || child is Button)
                        {
                            formElements.Add(child);
                        }
                    }
                }

                // Animate each element with a slight delay
                for (int i = 0; i < formElements.Count; i++)
                {
                    var element = formElements[i];
                    element.Opacity = 0;
                    element.TranslationY = 20;

                    // Stagger the animations
                    _ = Task.Run(async () =>
                    {
                        await Task.Delay(i * 100);
                        await element.FadeTo(1, 300, Easing.CubicOut);
                        await element.TranslateTo(0, 0, 300, Easing.CubicOut);
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Form elements animation failed: {ex.Message}");
            }
        }
         

        private void GoToSignup(object sender, EventArgs e)
        {
              Shell.Current.GoToAsync("//signin"); 
        } 
    }
}