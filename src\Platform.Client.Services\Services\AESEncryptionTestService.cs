using Microsoft.Extensions.Logging;
using System.Security.Claims;
using DeepMessage.MauiApp.Services;
using Platform.Framework.Core;

namespace Platform.Client.Services.Services;

/// <summary>
/// Test service to verify AES encryption system integration
/// This service can be used to test the complete AES encryption flow
/// </summary>
public interface IAESEncryptionTestService
{
    /// <summary>
    /// Tests the complete AES encryption flow for a friendship
    /// </summary>
    /// <param name="friendId">The friend's user ID</param>
    /// <returns>Test result with details</returns>
    Task<AESTestResult> TestAESEncryptionFlowAsync(string friendId);

    /// <summary>
    /// Tests AES key generation and storage
    /// </summary>
    /// <param name="friendId">The friend's user ID</param>
    /// <returns>Test result with details</returns>
    Task<AESTestResult> TestAESKeyGenerationAsync(string friendId);

    /// <summary>
    /// Tests message encryption and decryption
    /// </summary>
    /// <param name="friendId">The friend's user ID</param>
    /// <param name="testMessage">Test message to encrypt/decrypt</param>
    /// <returns>Test result with details</returns>
    Task<AESTestResult> TestMessageEncryptionAsync(string friendId, string testMessage = "Test AES encryption message");
}

/// <summary>
/// Test result for AES encryption operations
/// </summary>
public class AESTestResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> Details { get; set; } = new();
    public Exception? Exception { get; set; }
}

/// <summary>
/// Implementation of AES encryption test service
/// </summary>
public class AESEncryptionTestService : IAESEncryptionTestService
{
    private readonly IAESKeyManagerService _aesKeyManagerService;
    private readonly IClientEncryptionService _encryptionService;
    private readonly ILocalStorageService _localStorageService;
    private readonly ILogger<AESEncryptionTestService> _logger;

    public AESEncryptionTestService(
        IAESKeyManagerService aesKeyManagerService,
        IClientEncryptionService encryptionService,
        ILocalStorageService localStorageService,
        ILogger<AESEncryptionTestService> logger)
    {
        _aesKeyManagerService = aesKeyManagerService;
        _encryptionService = encryptionService;
        _localStorageService = localStorageService;
        _logger = logger;
    }

    public async Task<AESTestResult> TestAESEncryptionFlowAsync(string friendId)
    {
        var result = new AESTestResult();
        
        try
        {
            _logger.LogInformation("Starting AES encryption flow test for friend {FriendId}", friendId);

            // Test 1: Key Generation
            var keyGenResult = await TestAESKeyGenerationAsync(friendId);
            if (!keyGenResult.Success)
            {
                result.Success = false;
                result.Message = $"Key generation failed: {keyGenResult.Message}";
                result.Exception = keyGenResult.Exception;
                return result;
            }

            // Test 2: Message Encryption/Decryption
            var encryptionResult = await TestMessageEncryptionAsync(friendId);
            if (!encryptionResult.Success)
            {
                result.Success = false;
                result.Message = $"Message encryption failed: {encryptionResult.Message}";
                result.Exception = encryptionResult.Exception;
                return result;
            }

            result.Success = true;
            result.Message = "AES encryption flow test completed successfully";
            result.Details["KeyGeneration"] = keyGenResult.Details;
            result.Details["MessageEncryption"] = encryptionResult.Details;

            _logger.LogInformation("AES encryption flow test completed successfully for friend {FriendId}", friendId);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"AES encryption flow test failed: {ex.Message}";
            result.Exception = ex;
            _logger.LogError(ex, "AES encryption flow test failed for friend {FriendId}", friendId);
        }

        return result;
    }

    public async Task<AESTestResult> TestAESKeyGenerationAsync(string friendId)
    {
        var result = new AESTestResult();
        
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                result.Success = false;
                result.Message = "User not authenticated";
                return result;
            }

            // Generate friendship ID
            var friendshipId = CombineGuidsXor(userId, friendId);
            
            _logger.LogDebug("Testing AES key generation for friendship {FriendshipId}", friendshipId);

            // Test key generation
            var startTime = DateTime.UtcNow;
            var aesKey = await _aesKeyManagerService.GetOrCreateAESKeyAsync(friendshipId, userId, friendId);
            var endTime = DateTime.UtcNow;

            if (aesKey == null || aesKey.Length != 32) // AES-256 = 32 bytes
            {
                result.Success = false;
                result.Message = $"Invalid AES key generated. Length: {aesKey?.Length ?? 0}, Expected: 32";
                return result;
            }

            // Test key retrieval
            var retrievedKey = await _aesKeyManagerService.GetAESKeyForDecryptionAsync(friendshipId, true);
            if (retrievedKey == null)
            {
                result.Success = false;
                result.Message = "Failed to retrieve AES key after generation";
                return result;
            }

            // Verify keys match
            if (!aesKey.SequenceEqual(retrievedKey))
            {
                result.Success = false;
                result.Message = "Generated and retrieved AES keys do not match";
                return result;
            }

            result.Success = true;
            result.Message = "AES key generation test successful";
            result.Details["FriendshipId"] = friendshipId;
            result.Details["KeyLength"] = aesKey.Length;
            result.Details["GenerationTime"] = (endTime - startTime).TotalMilliseconds;
            result.Details["KeysMatch"] = true;

            _logger.LogDebug("AES key generation test successful for friendship {FriendshipId}", friendshipId);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"AES key generation test failed: {ex.Message}";
            result.Exception = ex;
            _logger.LogError(ex, "AES key generation test failed for friend {FriendId}", friendId);
        }

        return result;
    }

    public async Task<AESTestResult> TestMessageEncryptionAsync(string friendId, string testMessage = "Test AES encryption message")
    {
        var result = new AESTestResult();
        
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                result.Success = false;
                result.Message = "User not authenticated";
                return result;
            }

            // Generate friendship ID
            var friendshipId = CombineGuidsXor(userId, friendId);
            
            _logger.LogDebug("Testing message encryption for friendship {FriendshipId}", friendshipId);

            // Get AES key
            var aesKey = await _aesKeyManagerService.GetOrCreateAESKeyAsync(friendshipId, userId, friendId);
            if (aesKey == null)
            {
                result.Success = false;
                result.Message = "Failed to get AES key for encryption test";
                return result;
            }

            // Test encryption
            var startTime = DateTime.UtcNow;
            var encryptedMessage = _encryptionService.EncryptWithAESAsync(testMessage, aesKey);
            var encryptionTime = DateTime.UtcNow;

            if (string.IsNullOrEmpty(encryptedMessage))
            {
                result.Success = false;
                result.Message = "Message encryption returned null or empty result";
                return result;
            }

            // Test decryption
            var decryptedMessage = _encryptionService.DecryptWithAESAsync(encryptedMessage, aesKey);
            var endTime = DateTime.UtcNow;

            if (decryptedMessage != testMessage)
            {
                result.Success = false;
                result.Message = $"Decrypted message does not match original. Original: '{testMessage}', Decrypted: '{decryptedMessage}'";
                return result;
            }

            result.Success = true;
            result.Message = "Message encryption test successful";
            result.Details["OriginalMessage"] = testMessage;
            result.Details["EncryptedMessage"] = encryptedMessage;
            result.Details["DecryptedMessage"] = decryptedMessage;
            result.Details["EncryptionTime"] = (encryptionTime - startTime).TotalMilliseconds;
            result.Details["DecryptionTime"] = (endTime - encryptionTime).TotalMilliseconds;
            result.Details["TotalTime"] = (endTime - startTime).TotalMilliseconds;

            _logger.LogDebug("Message encryption test successful for friendship {FriendshipId}", friendshipId);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Message encryption test failed: {ex.Message}";
            result.Exception = ex;
            _logger.LogError(ex, "Message encryption test failed for friend {FriendId}", friendId);
        }

        return result;
    }

    /// <summary>
    /// Generates a deterministic friendship ID by combining two user IDs
    /// </summary>
    /// <param name="guid1">First user ID</param>
    /// <param name="guid2">Second user ID</param>
    /// <returns>Combined friendship ID</returns>
    private string CombineGuidsXor(string guid1, string guid2)
    {
        string combined = string.Empty;
        for (int i = 0; i < guid1.Length; i++)
        {
            combined += (i % 2 == 0) ? guid1[i] : guid2[i];
        }
        return combined;
    }
}
