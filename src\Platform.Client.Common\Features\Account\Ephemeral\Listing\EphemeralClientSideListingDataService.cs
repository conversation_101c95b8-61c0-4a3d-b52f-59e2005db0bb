﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.MauiShared;
using DeepMessage.MauiApp.Helpers;
using DeepMessage.ServiceContracts.Features.Account;
namespace Platform.Client.Common.Features.Account;
public class EphemeralClientSideListingDataService : IEphemeralListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public EphemeralClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<PagedDataList<EphemeralListingBusinessObject>> GetPaginatedItems(EphemeralFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<EphemeralListingBusinessObject>>($"api/EphemeralListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
