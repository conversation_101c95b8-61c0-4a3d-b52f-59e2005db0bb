using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Security.Cryptography;

namespace Platform.Client.Services.Services;

/// <summary>
/// Interface for AES key caching service
/// Provides in-memory caching of decrypted AES keys for performance
/// </summary>
public interface IAESKeyCacheService
{
    /// <summary>
    /// Gets a decrypted AES key from cache
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    /// <returns>Decrypted AES key or null if not cached</returns>
    byte[]? GetDecryptedAESKeyAsync(string friendshipId);

    /// <summary>
    /// Caches a decrypted AES key
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    /// <param name="aesKey">The decrypted AES key</param>
    void CacheDecryptedAESKeyAsync(string friendshipId, byte[] aesKey);

    /// <summary>
    /// Clears all cached keys
    /// </summary>
    void ClearCache();

    /// <summary>
    /// Clears a specific friendship's cached key
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    void ClearFriendshipKey(string friendshipId);

    /// <summary>
    /// Gets cache statistics for monitoring
    /// </summary>
    CacheStatistics GetCacheStatistics();
}

/// <summary>
/// Cache statistics for monitoring and debugging
/// </summary>
public class CacheStatistics
{
    public int TotalCachedKeys { get; set; }
    public DateTime LastAccessed { get; set; }
    public int HitCount { get; set; }
    public int MissCount { get; set; }
}

/// <summary>
/// Cached AES key with metadata
/// </summary>
internal class CachedAESKey
{
    public byte[] Key { get; set; } = null!;
    public DateTime CachedAt { get; set; }
    public DateTime LastAccessed { get; set; }
    public int AccessCount { get; set; }
}

/// <summary>
/// In-memory AES key cache service implementation
/// Provides secure caching with automatic cleanup and memory management
/// </summary>
public class AESKeyCacheService : IAESKeyCacheService, IDisposable
{
    private readonly ConcurrentDictionary<string, CachedAESKey> _keyCache = new();
    private readonly object _lockObject = new object();
    private readonly ILogger<AESKeyCacheService> _logger;
    private readonly Timer _cleanupTimer;
    private bool _disposed = false;

    // Cache configuration
    private const int MAX_CACHE_SIZE = 100;
    private const int CLEANUP_INTERVAL_MINUTES = 15;
    private const int MAX_KEY_AGE_HOURS = 2;

    // Statistics
    private int _hitCount = 0;
    private int _missCount = 0;
    private DateTime _lastAccessed = DateTime.MinValue;

    public AESKeyCacheService(ILogger<AESKeyCacheService> logger)
    {
        _logger = logger;

        // Setup cleanup timer
        _cleanupTimer = new Timer(CleanupExpiredKeys, null,
            TimeSpan.FromMinutes(CLEANUP_INTERVAL_MINUTES),
            TimeSpan.FromMinutes(CLEANUP_INTERVAL_MINUTES));
    }

    public byte[]? GetDecryptedAESKeyAsync(string friendshipId)
    {
        if (string.IsNullOrEmpty(friendshipId))
        {
            Interlocked.Increment(ref _missCount);
            return null;
        }

        lock (_lockObject)
        {
            if (_keyCache.TryGetValue(friendshipId, out var cachedKey))
            {
                cachedKey.LastAccessed = DateTime.UtcNow;
                cachedKey.AccessCount++;
                _lastAccessed = DateTime.UtcNow;
                Interlocked.Increment(ref _hitCount);

                _logger.LogDebug("AES key cache hit for friendship {FriendshipId}", friendshipId);

                // Return a copy of the key for security
                var keyCopy = new byte[cachedKey.Key.Length];
                Array.Copy(cachedKey.Key, keyCopy, cachedKey.Key.Length);
                return keyCopy;
            }

            Interlocked.Increment(ref _missCount);
            _logger.LogDebug("AES key cache miss for friendship {FriendshipId}", friendshipId);
            return null;
        }
    }

    public void CacheDecryptedAESKeyAsync(string friendshipId, byte[] aesKey)
    {
        if (string.IsNullOrEmpty(friendshipId) || aesKey == null || aesKey.Length == 0)
        {
            return;
        }

        lock (_lockObject)
        {
            // Check cache size limit
            if (_keyCache.Count >= MAX_CACHE_SIZE && !_keyCache.ContainsKey(friendshipId))
            {
                // Remove oldest accessed key
                var oldestKey = _keyCache.Values.OrderBy(k => k.LastAccessed).FirstOrDefault();
                if (oldestKey != null)
                {
                    var keyToRemove = _keyCache.FirstOrDefault(kvp => kvp.Value == oldestKey);
                    if (!keyToRemove.Equals(default(KeyValuePair<string, CachedAESKey>)))
                    {
                        _keyCache.TryRemove(keyToRemove.Key, out var removedKey);
                        SecureClearKey(removedKey?.Key);
                    }
                }
            }

            // Store a copy of the key for security
            var keyCopy = new byte[aesKey.Length];
            Array.Copy(aesKey, keyCopy, aesKey.Length);

            var cachedKey = new CachedAESKey
            {
                Key = keyCopy,
                CachedAt = DateTime.UtcNow,
                LastAccessed = DateTime.UtcNow,
                AccessCount = 0
            };

            _keyCache.AddOrUpdate(friendshipId, cachedKey, (key, oldValue) =>
            {
                SecureClearKey(oldValue.Key);
                return cachedKey;
            });

            _logger.LogDebug("AES key cached for friendship {FriendshipId}", friendshipId);
        }
    }

    public void ClearCache()
    {
        lock (_lockObject)
        {
            foreach (var cachedKey in _keyCache.Values)
            {
                SecureClearKey(cachedKey.Key);
            }
            _keyCache.Clear();
            _logger.LogInformation("AES key cache cleared");
        }
    }

    public void ClearFriendshipKey(string friendshipId)
    {
        if (string.IsNullOrEmpty(friendshipId))
            return;

        lock (_lockObject)
        {
            if (_keyCache.TryRemove(friendshipId, out var cachedKey))
            {
                SecureClearKey(cachedKey.Key);
                _logger.LogDebug("AES key cleared from cache for friendship {FriendshipId}", friendshipId);
            }
        }
    }

    public CacheStatistics GetCacheStatistics()
    {
        lock (_lockObject)
        {
            return new CacheStatistics
            {
                TotalCachedKeys = _keyCache.Count,
                LastAccessed = _lastAccessed,
                HitCount = _hitCount,
                MissCount = _missCount
            };
        }
    }

    private void CleanupExpiredKeys(object? state)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow.AddHours(-MAX_KEY_AGE_HOURS);
            var expiredKeys = new List<string>();

            lock (_lockObject)
            {
                foreach (var kvp in _keyCache)
                {
                    if (kvp.Value.CachedAt < cutoffTime)
                    {
                        expiredKeys.Add(kvp.Key);
                    }
                }

                foreach (var key in expiredKeys)
                {
                    if (_keyCache.TryRemove(key, out var cachedKey))
                    {
                        SecureClearKey(cachedKey.Key);
                    }
                }
            }

            if (expiredKeys.Count > 0)
            {
                _logger.LogDebug("Cleaned up {Count} expired AES keys from cache", expiredKeys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during AES key cache cleanup");
        }
    }

    private static void SecureClearKey(byte[]? key)
    {
        if (key != null)
        {
            // Securely clear the key from memory
            RandomNumberGenerator.Fill(key);
            Array.Clear(key, 0, key.Length);
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            ClearCache();
            _disposed = true;
        }
    }
}
