﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
namespace Platform.Client.Services.Features.Account;
public class SignInFormViewModel : ObservableBase, IValidateable
{
    public string? NickName { get; set; }

    [Required(ErrorMessage = "Code is required")]
    [MinLength(6, ErrorMessage = "Enter a Valid Code")]
    private string? _passKey;

    public string? PassKey
    {
        get { return _passKey; }
        set
        {
            _passKey = value;
            SetField(ref _passKey, value);
        }
    }

    public string? DeviceString { get; set; }

    private bool _showPassword;
    public bool ShowPassword
    {
        get { return _showPassword; }
        set
        {
            SetField(ref _showPassword, value);
            NotifyPropertyChanged(nameof(IsPasswordHidden));
        }
    }

    public bool IsPasswordHidden => !ShowPassword;
      
    public string NickNameHint
    {
        get { return string.IsNullOrEmpty(NickName) ? "This device is not registered" : $"Registered - ******{NickName.Last()}"; }
    }

    public string? DeviceToken { get; set; }
    public string? DeviceName { get; set; }
    public string? Platform { get; set; }

    public void Validate()
    {
        if (string.IsNullOrEmpty(PassKey))
        {
            throw new ValidationException("Password is required");
        }
    }
}
