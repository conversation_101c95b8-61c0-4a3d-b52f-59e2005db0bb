﻿using DeepMessage.Framework.Core;
namespace DeepMessage.ServiceContracts.Features.Account;

/// <summary>
/// Filter business object for AES key management queries
/// Uses SearchKey for specific friendship ID lookup
/// </summary>
public class EphemeralFilterBusinessObject : BaseFilterBusinessObject
{
    /// <summary>
    /// Filter by specific user ID (friendship owner)
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Filter by specific friend ID
    /// </summary>
    public string? FriendId { get; set; }

    /// <summary>
    /// Filter by AES key active status
    /// </summary>
    public bool? AESKeyActive { get; set; }

    // Note: SearchKey from BaseFilterBusinessObject is used for specific friendship ID lookup
}
