using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Security.Claims;
using Microsoft.Extensions.DependencyInjection;
using DeepMessage.MauiApp.Services;
using Platform.Framework.Core;

namespace Platform.Client.Services.Services;

/// <summary>
/// Interface for AES key management service
/// Orchestrates AES key generation, encryption, storage, and retrieval
/// </summary>
public interface IAESKeyManagerService
{
    /// <summary>
    /// Gets or creates an AES key for a friendship
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    /// <param name="senderId">The sender's user ID</param>
    /// <param name="receiverId">The receiver's user ID</param>
    /// <returns>Decrypted AES key for immediate use</returns>
    Task<byte[]> GetOrCreateAESKeyAsync(string friendshipId, string senderId, string receiverId);

    /// <summary>
    /// Gets an AES key for decryption
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    /// <param name="isSender">Whether the current user is the sender</param>
    /// <returns>Decrypted AES key or null if not available</returns>
    Task<byte[]?> GetAESKeyForDecryptionAsync(string friendshipId, bool isSender);

    /// <summary>
    /// Invalidates AES keys for a friendship
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    Task InvalidateAESKeysAsync(string friendshipId);

    /// <summary>
    /// Regenerates AES keys for a friendship
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    /// <param name="senderId">The sender's user ID</param>
    /// <param name="receiverId">The receiver's user ID</param>
    Task RegenerateAESKeysAsync(string friendshipId, string senderId, string receiverId);
}

/// <summary>
/// AES key manager service implementation
/// Handles the complete lifecycle of AES keys for friendship-based encryption
/// </summary>
public class AESKeyManagerService : IAESKeyManagerService
{
    private readonly IEphemeralFormDataService _formService;
    private readonly IAESKeyCacheService _cacheService;
    private readonly IClientEncryptionService _encryptionService;
    private readonly ISecureKeyManager _secureKeyManager;
    private readonly IFriendsListingDataService _friendsListingService;
    private readonly ILocalStorageService _localStorageService;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AESKeyManagerService> _logger;

    public AESKeyManagerService(
        IEphemeralFormDataService formService,
        IAESKeyCacheService cacheService,
        IClientEncryptionService encryptionService,
        ISecureKeyManager secureKeyManager,
        IFriendsListingDataService friendsListingService,
        ILocalStorageService localStorageService,
        IServiceProvider serviceProvider,
        ILogger<AESKeyManagerService> logger)
    {
        _formService = formService;
        _cacheService = cacheService;
        _encryptionService = encryptionService;
        _secureKeyManager = secureKeyManager;
        _friendsListingService = friendsListingService;
        _localStorageService = localStorageService;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task<byte[]> GetOrCreateAESKeyAsync(string friendshipId, string senderId, string receiverId)
    {
        try
        {
            // 1. Check cache first
            var cachedKey = _cacheService.GetDecryptedAESKeyAsync(friendshipId);
            if (cachedKey != null)
            {
                _logger.LogDebug("Using cached AES key for friendship {FriendshipId}", friendshipId);
                return cachedKey;
            }

            var friendshipData = await _formService.GetItemByIdAsync(friendshipId);

            // 3. If keys exist and active, decrypt and cache
            if (!string.IsNullOrEmpty(friendshipData?.SenderAESKey))
            {
                var decryptedKey = await DecryptAESKeyAsync(friendshipData.SenderAESKey, true);
                if (decryptedKey != null)
                {
                    _cacheService.CacheDecryptedAESKeyAsync(friendshipId, decryptedKey);
                    _logger.LogDebug("Retrieved and cached existing AES key for friendship {FriendshipId}", friendshipId);
                    return decryptedKey;
                }
            }
            _logger.LogInformation("Generating new AES key for friendship {FriendshipId}", friendshipId);
            return await GenerateAndStoreNewAESKeyAsync(friendshipId, senderId, receiverId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting or creating AES key for friendship {FriendshipId}", friendshipId);
            throw;
        }
    }

    public async Task<byte[]?> GetAESKeyForDecryptionAsync(string friendshipId, bool isSender)
    {
        try
        {
            // 1. Check cache first
            var cachedKey = _cacheService.GetDecryptedAESKeyAsync(friendshipId);
            if (cachedKey != null)
            {
                return cachedKey;
            }

            var friendshipData = await _formService.GetItemByIdAsync(friendshipId);
            if (friendshipData == null || friendshipData.SenderAESKey == null)
            {
                var ephemeralFormDataService = _serviceProvider.GetRequiredKeyedService<IEphemeralFormDataService>("client");
                friendshipData = await ephemeralFormDataService.GetItemByIdAsync(friendshipId);

                if (friendshipData == null)
                {
                    _logger.LogWarning("No friendship data found for friendship {FriendshipId}", friendshipId);
                    return null;
                }

                if (friendshipData.SenderAESKey == null && friendshipData.ReceiverAESKey == null)
                {
                    _logger.LogWarning("No AES keys found for friendship {FriendshipId}", friendshipId);
                    return null;
                }
                await _formService.SaveAsync(friendshipData);
            }

            // 3. Decrypt appropriate key based on sender/receiver role
            var encryptedKey = isSender ? friendshipData.SenderAESKey : friendshipData.ReceiverAESKey;
            if (string.IsNullOrEmpty(encryptedKey))
            {
                _logger.LogWarning("No {Role} AES key found for friendship {FriendshipId}",
                    isSender ? "sender" : "receiver", friendshipId);
                return null;
            }

            var decryptedKey = await DecryptAESKeyAsync(encryptedKey, isSender);
            if (decryptedKey != null)
            {
                _cacheService.CacheDecryptedAESKeyAsync(friendshipId, decryptedKey);
            }

            return decryptedKey;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting AES key for decryption, friendship {FriendshipId}", friendshipId);
            return null;
        }
    }

    public async Task InvalidateAESKeysAsync(string friendshipId)
    {
        try
        {
            var formData = new EphemeralFormBusinessObject
            {
                FriendshipId = friendshipId,
                Operation = "invalidate"
            };

            await _formService.SaveAsync(formData);
            _cacheService.ClearFriendshipKey(friendshipId);

            _logger.LogInformation("Invalidated AES keys for friendship {FriendshipId}", friendshipId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating AES keys for friendship {FriendshipId}", friendshipId);
            throw;
        }
    }

    public async Task RegenerateAESKeysAsync(string friendshipId, string senderId, string receiverId)
    {
        try
        {
            // Clear cache first
            _cacheService.ClearFriendshipKey(friendshipId);

            // Generate new keys
            await GenerateAndStoreNewAESKeyAsync(friendshipId, senderId, receiverId);

            _logger.LogInformation("Regenerated AES keys for friendship {FriendshipId}", friendshipId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating AES keys for friendship {FriendshipId}", friendshipId);
            throw;
        }
    }

    private async Task<byte[]> GenerateAndStoreNewAESKeyAsync(string friendshipId, string senderId, string receiverId)
    {
        // Generate new AES-256 key using the encryption service
        var aesKey = _encryptionService.GenerateAES256Key();

        // Get user public keys for encryption
        var senderPublicKey = await GetUserPublicKeyAsync(senderId);
        var receiverPublicKey = await GetUserPublicKeyAsync(receiverId);

        if (string.IsNullOrEmpty(senderPublicKey) || string.IsNullOrEmpty(receiverPublicKey))
        {
            throw new InvalidOperationException("Unable to retrieve user public keys for AES key encryption");
        }

        // Encrypt AES key with both users' RSA public keys
        var senderEncryptedKey = _encryptionService.EncryptWithRSAPublicKey(Convert.ToBase64String(aesKey), senderPublicKey);
        var receiverEncryptedKey = _encryptionService.EncryptWithRSAPublicKey(Convert.ToBase64String(aesKey), receiverPublicKey);

        // Store encrypted keys
        var formData = new EphemeralFormBusinessObject
        {
            FriendshipId = friendshipId,
            SenderAESKey = senderEncryptedKey,
            ReceiverAESKey = receiverEncryptedKey,
            Operation = "create"
        };

        await _formService.SaveAsync(formData);

        // Cache the decrypted key
        _cacheService.CacheDecryptedAESKeyAsync(friendshipId, aesKey);

        _logger.LogDebug("Generated and stored new AES key for friendship {FriendshipId}", friendshipId);
        return aesKey;
    }

    private async Task<byte[]?> DecryptAESKeyAsync(string encryptedKey, bool isSender)
    {
        try
        {
            if (!_secureKeyManager.IsRSAKeyAvailable())
            {
                _logger.LogWarning("RSA private key not available for AES key decryption");
                return null;
            }

            using var privateKey = _secureKeyManager.GetRSAPrivateKeyAsync();
            var decryptedKeyBase64 = _encryptionService.DecryptWithRSAPrivateKey(encryptedKey, privateKey);
            return Convert.FromBase64String(decryptedKeyBase64);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrypting AES key");
            return null;
        }
    }

    private async Task<string?> GetUserPublicKeyAsync(string userId)
    {
        try
        {
            var currentUserId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);

            // If this is the current user, get their public key from the secure key manager
            if (userId == currentUserId && _secureKeyManager.IsRSAKeyAvailable())
            {
                using var privateKey = _secureKeyManager.GetRSAPrivateKeyAsync();
                var publicKeyBytes = privateKey.ExportRSAPublicKey();

                // Convert to PEM format for consistency
                var publicKeyPem = Convert.ToBase64String(publicKeyBytes);

                _logger.LogDebug("Retrieved public key for current user {UserId}", userId);
                return publicKeyPem;
            }

            // For other users, query friends listing to find their public key
            var filter = new FriendsFilterBusinessObject
            {
                UsePagination = false
            };

            var friendsResult = await _friendsListingService.GetPaginatedItems(filter);

            // Find the friend with the matching user ID
            var friend = friendsResult.Items?.FirstOrDefault(f => f.FriendId == userId);

            if (friend != null && !string.IsNullOrEmpty(friend.Pub1))
            {
                _logger.LogDebug("Retrieved public key for user {UserId} from friends listing", userId);
                return friend.Pub1;
            }

            _logger.LogWarning("Unable to retrieve public key for user {UserId}", userId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving public key for user {UserId}", userId);
            return null;
        }
    }
}
