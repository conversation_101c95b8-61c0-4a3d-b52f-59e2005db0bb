﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Server.DataServices.Helpers;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatMessagesSyncServerSideFormDataService : IChatMessagesSyncFormDataService
{

    private readonly AppDbContext _context;
    private readonly HybridCache hybridCache;
    private readonly MessageDispatcher messageDispatcher;

    public ChatMessagesSyncServerSideFormDataService(AppDbContext context,
        HybridCache hybridCache,
        MessageDispatcher messageDispatcher)
    {
        _context = context;
        this.hybridCache = hybridCache;
        this.messageDispatcher = messageDispatcher;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatMessagesSyncFormBusinessObject formBusinessObject)
    {

        var message = _context.Messages.FirstOrDefault(x => x.Id == formBusinessObject.Id);
        if (message == null)
        {
            message = new Message()
            {
                Id = formBusinessObject.Id,
                ConversationId = formBusinessObject.ConversationId,
                CreatedAt = formBusinessObject.CreatedAt,
                DeletedAt = formBusinessObject.DeletedAt,
                DisappearAfter = formBusinessObject.DisappearAfter,
                DisappearAt = formBusinessObject.DisappearAt,
                IsDeleted = formBusinessObject.IsDeleted,
                IsEdited = formBusinessObject.IsEdited,
                IsEphemeral = formBusinessObject.IsEphemeral,
                EditedAt = formBusinessObject.EditedAt,
                PlainContent = null, // ✅ SECURE: Never store plaintext on server for E2E encryption
                SenderId = formBusinessObject.SenderId,
                //todo: sender device id for multi device support
                DeliveryStatus = MessageDeliveryStatus.SentToMessageServer,
                DeliveryStatusTime = DateTime.UtcNow,
            };
            _context.Messages.Add(message);
            await _context.SaveChangesAsync();

            // ✅ SECURE: Save encrypted MessageRecipient records from sync data
            foreach (var recipientData in formBusinessObject.MessageRecipients)
            {
                var existingRecipient = await _context.MessageRecipients.FirstOrDefaultAsync(
                    x => x.MessageId == message.Id && x.RecipientId == recipientData.RecipientId);

                if (existingRecipient == null)
                {
                    var messageRecipient = new MessageRecipient()
                    {
                        Id = recipientData.Id,
                        MessageId = message.Id,
                        UserDeviceId = string.Empty, // TODO: Add device tracking
                        RecipientId = recipientData.RecipientId,
                        EncryptedContent = recipientData.EncryptedContent, // ✅ SECURE: Use encrypted content
                        MessageDeliveryStatus = recipientData.DeliveryStatus,
                        DeliveryStatusTime = recipientData.DeliveryStatusTime,
                        IsRead = recipientData.IsRead,
                        ReadAt = recipientData.ReadAt
                    };
                    _context.MessageRecipients.Add(messageRecipient);
                }
            }
            await _context.SaveChangesAsync();
        }

        var pendingMessages = (from m in _context.Messages
                               where m.Id == message.Id
                               select new ChatMessagesSyncFormBusinessObject
                               {
                                   Id = m.Id,
                                   ConversationId = m.ConversationId,
                                   SenderId = m.SenderId,
                                   CreatedAt = m.CreatedAt,
                                   DeletedAt = m.DeletedAt,
                                   DisappearAfter = m.DisappearAfter,
                                   DisappearAt = m.DisappearAt,
                                   EditedAt = m.EditedAt,
                                   IsDeleted = m.IsDeleted,
                                   IsEdited = m.IsEdited,
                                   IsEphemeral = m.IsEphemeral,
                                   EnableFallBackChannel = true,
                                   MessageRecipients = m.Recipients.Select(r => new MessageRecipientSyncFormBusinessObject()
                                   {
                                       DeliveryStatus = r.MessageDeliveryStatus,
                                       DeliveryStatusTime = r.DeliveryStatusTime,
                                       EncryptedContent = r.EncryptedContent,
                                       Id = r.Id,
                                       IsRead = r.IsRead,
                                       ReadAt = r.ReadAt,
                                       RecipientId = r.RecipientId,
                                       MessageId = r.MessageId
                                   }).ToList()
                               }).ToList();
        foreach (var item in pendingMessages)
        {
            messageDispatcher.DispatchMessage(item);
        }
        return message.Id;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ChatMessagesSyncFormBusinessObject> GetItemByIdAsync(string id)
    {
        var query = (from m in _context.Messages
                     where m.Id == id
                     select new ChatMessagesSyncFormBusinessObject()
                     {
                         Id = m.Id,
                         ConversationId = m.ConversationId,
                         SenderId = m.SenderId,
                         CreatedAt = m.CreatedAt,
                         DeletedAt = m.DeletedAt,
                         DisappearAfter = m.DisappearAfter,
                         DisappearAt = m.DisappearAt,
                         EditedAt = m.EditedAt,
                         IsDeleted = m.IsDeleted,
                         IsEdited = m.IsEdited,
                         IsEphemeral = m.IsEphemeral,
                         PlainContent = null // ✅ SECURE: Never expose plaintext in queries
                     });
        var chatMessage = await query.FirstAsync();
        return chatMessage;
    }
}
