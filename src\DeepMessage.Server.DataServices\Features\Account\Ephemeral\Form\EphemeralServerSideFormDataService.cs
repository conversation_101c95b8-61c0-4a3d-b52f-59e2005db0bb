﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace DeepMessage.Server.DataServices.Features.Account;

public class EphemeralServerSideFormDataService : IEphemeralFormDataService
{
    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor _contextAccessor;
    private readonly ILogger<EphemeralServerSideFormDataService> _logger;

    public EphemeralServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor, ILogger<EphemeralServerSideFormDataService> logger)
    {
        _context = context;
        _contextAccessor = contextAccessor;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(EphemeralFormBusinessObject formBusinessObject)
    {
        var userId = _contextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        if (string.IsNullOrEmpty(userId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Find the friendship record
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => f.Id == formBusinessObject.FriendshipId && (f.UserId == userId || f.FriendId == userId));

            if (friendship == null)
            {
                throw new InvalidOperationException($"Friendship not found: {formBusinessObject.FriendshipId}");
            }

            if (string.IsNullOrEmpty(formBusinessObject.SenderAESKey) || string.IsNullOrEmpty(formBusinessObject.ReceiverAESKey))
            {
                throw new ArgumentException("SenderAESKey and ReceiverAESKey cannot be null or empty");
            }

            // Update AES key fields based on operation
            switch (formBusinessObject.Operation.ToLower())
            {
                case "create":
                case "update":
                    friendship.SenderAESKey = formBusinessObject.SenderAESKey;
                    friendship.ReceiverAESKey = formBusinessObject.ReceiverAESKey;
                    friendship.AESKeyCreatedAt = DateTime.UtcNow;
                    friendship.AESKeyActive = true;
                    break;

                case "invalidate":
                    friendship.AESKeyActive = false;
                    break;

                default:
                    throw new ArgumentException($"Invalid operation: {formBusinessObject.Operation}");
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("AES keys {Operation} for friendship {FriendshipId} by user {UserId}",
                formBusinessObject.Operation, formBusinessObject.FriendshipId, userId);

            return friendship.Id;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error saving AES keys for friendship {FriendshipId}", formBusinessObject.FriendshipId);
            throw;
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<EphemeralFormBusinessObject> GetItemByIdAsync(string id)
    {
        var userId = _contextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        if (string.IsNullOrEmpty(userId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        var friendship = await _context.Friendships.FirstOrDefaultAsync(f => f.Id == id);
        return new EphemeralFormBusinessObject
        {
            FriendshipId = friendship?.Id,
            SenderAESKey = friendship?.SenderAESKey,
            ReceiverAESKey = friendship?.ReceiverAESKey,
            Operation = "update"
        };
    }
}
