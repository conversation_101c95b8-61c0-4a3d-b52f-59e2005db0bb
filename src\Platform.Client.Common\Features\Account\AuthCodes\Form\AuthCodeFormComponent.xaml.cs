﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using Platform.Client.Services.Features.AuthCodes;
namespace Platform.Client.Common.Features.AuthCodes;
public class AuthCodeFormViewBase : FormBaseMaui<AuthCodeFormBusinessObject, AuthCodeFormViewModel, string, IAuthCodeFormDataService>
{
    public AuthCodeFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

public partial class AuthCodeFormView : AuthCodeFormViewBase
{
    public AuthCodeFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        await AnimateEntrance();
    }

    /// <summary>
    /// Animates the entrance of the modal with fade-in and scale effects
    /// </summary>
    private async Task AnimateEntrance()
    {
        try
        {
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                // Small delay to ensure the page is loaded
                await Task.Delay(100);

                // Animate entrance with fade, scale, and slide up
                var fadeTask = mainBorder.FadeTo(1, 500, Easing.CubicOut);
                var scaleTask = mainBorder.ScaleTo(1, 500, Easing.CubicOut);
                var translateTask = mainBorder.TranslateTo(0, 0, 500, Easing.CubicOut);

                await Task.WhenAll(fadeTask, scaleTask, translateTask);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Entrance animation failed: {ex.Message}");
            // Ensure visibility if animation fails
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                mainBorder.Opacity = 1;
                mainBorder.Scale = 1;
                mainBorder.TranslationY = 0;
            }
        }
    }

    public override Task OnAfterSaveAsync(string key)
    {
        SelectedItem.AuthCode = key;

        // ✅ STREAMLINED UX: Display code directly in form instead of modal
        return base.OnAfterSaveAsync(key);
    }

    /// <summary>
    /// Handles the close button click
    /// </summary>
    private async void OnCloseClicked(object sender, EventArgs e)
    {
        try
        {
            // Animate button feedback if it's a button
            if (sender is Button button)
            {
                await AnimateButtonTap(button);
            }

            await CloseModal();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Close navigation failed: {ex.Message}");
            // Fallback navigation
            await Navigation.PopModalAsync();
        }
    }

    /// <summary>
    /// Closes the modal with animation
    /// </summary>
    private async Task CloseModal()
    {
        try
        {
            await AnimateExit();
            await Navigation.PopModalAsync(false);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to close modal with animation: {ex.Message}");
            await Navigation.PopModalAsync();
        }
    }

    /// <summary>
    /// Animates the exit of the modal
    /// </summary>
    private async Task AnimateExit()
    {
        try
        {
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                // Animate the main content with fade out effects
                var fadeTask = mainBorder.FadeTo(0, 300, Easing.CubicOut);
                var scaleTask = mainBorder.ScaleTo(0.9, 300, Easing.CubicOut);
                var translateTask = mainBorder.TranslateTo(0, 30, 300, Easing.CubicOut);

                // Also fade the background
                var backgroundFadeTask = this.FadeTo(0.3, 300, Easing.CubicOut);

                // Wait for all animations to complete
                await Task.WhenAll(fadeTask, scaleTask, translateTask, backgroundFadeTask);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Exit animation failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles the copy button click
    /// </summary>
    private async void OnCopyClicked(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(SelectedItem?.AuthCode))
            {
                await DisplayAlert("Error", "No authentication code to copy", "OK");
                return;
            }

            // Animate button feedback
            if (sender is Button button)
            {
                await AnimateButtonTap(button);
            }

            // Copy to clipboard
            await Clipboard.SetTextAsync(SelectedItem.AuthCode);

            // Show success feedback
            await DisplayAlert("Copied", "Authentication code copied to clipboard", "OK");
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", "Failed to copy to clipboard", "OK");
            System.Diagnostics.Debug.WriteLine($"Copy failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles the share button click
    /// </summary>
    private async void OnShareClicked(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(SelectedItem?.AuthCode))
            {
                await DisplayAlert("Error", "No authentication code to share", "OK");
                return;
            }

            // Animate button feedback
            if (sender is Button button)
            {
                await AnimateButtonTap(button);
            }

            // Share the auth code
            var shareRequest = new ShareTextRequest
            {
                Text = $"Here's my authentication code: {SelectedItem.AuthCode}",
                Title = "Authentication Code"
            };

            await Share.RequestAsync(shareRequest);
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", "Failed to share authentication code", "OK");
            System.Diagnostics.Debug.WriteLine($"Share failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Animates button tap feedback
    /// </summary>
    private async Task AnimateButtonTap(Button button)
    {
        try
        {
            // Quick scale animation for tap feedback
            await button.ScaleTo(0.95, 100, Easing.CubicOut);
            await button.ScaleTo(1, 100, Easing.CubicIn);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Button tap animation failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Handle back button press
    /// </summary>
    protected override bool OnBackButtonPressed()
    {
        // Handle back button press with animation
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            await CloseModal();
        });
        return true; // Prevent default back button behavior
    }
}
