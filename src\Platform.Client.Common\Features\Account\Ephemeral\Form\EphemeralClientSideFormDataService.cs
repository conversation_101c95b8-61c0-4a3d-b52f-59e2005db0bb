﻿using System.Text;
using System.Text.Json;
using System.Net.Http.Json;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
namespace Platform.Client.Common.Features.Account;
public class EphemeralClientSideFormDataService : IEphemeralFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public EphemeralClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(EphemeralFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/EphemeralsForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<EphemeralFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<EphemeralFormBusinessObject>($"api/EphemeralsForm/GetItemById?id=" + id);
	}
}
