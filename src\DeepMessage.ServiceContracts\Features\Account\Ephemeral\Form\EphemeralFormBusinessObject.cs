﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
namespace DeepMessage.ServiceContracts.Features.Account;

/// <summary>
/// Form business object for AES key management operations
/// Used for creating and updating AES keys for friendship-based encryption
/// </summary>
public class EphemeralFormBusinessObject
{
    /// <summary>
    /// The friendship ID for which AES keys are being managed
    /// </summary>
    [Required]
    [StringLength(450)]
    public string? FriendshipId { get; set; } = null!;

    /// <summary>
    /// AES-256 key encrypted with sender's RSA public key
    /// Base64-encoded encrypted key data
    /// </summary>
    [StringLength(4000)]
    public string? SenderAESKey { get; set; }

    /// <summary>
    /// Same AES-256 key encrypted with receiver's RSA public key
    /// Base64-encoded encrypted key data
    /// </summary>
    [StringLength(4000)]
    public string? ReceiverAESKey { get; set; }

    /// <summary>
    /// Operation type: "create", "update", "invalidate"
    /// </summary>
    [StringLength(50)]
    public string Operation { get; set; } = "create";
}
