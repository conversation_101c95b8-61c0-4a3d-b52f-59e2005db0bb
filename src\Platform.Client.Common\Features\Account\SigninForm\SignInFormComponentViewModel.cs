﻿using Android.Telephony;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.ServiceContracts.Features.Configurations;
using Platform.Client.Services.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using Plugin.Firebase.CloudMessaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;

namespace Platform.Client.Common.Features.Account
{
    public class SignInFormComponentViewModel : FormBaseCommon<SignInFormBusinessObject, SignInFormViewModel, string, ISignInFormDataService>
    {
        private IClientEncryptionService _encryptionService;
        private ISecureKeyManager _secureKeyManager;
        private ILocalStorageService _storageService;

        private ICommand? _TogglePasswordVisibilityCommand;

        public ICommand? TogglePasswordVisibilityCommand
        {
            get { return _TogglePasswordVisibilityCommand; }
            set { SetField(ref _TogglePasswordVisibilityCommand, value); }
        }
         
        public static SignInFormComponentViewModel? Default { get; set; }

        public SignInFormComponentViewModel(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
        {
            // Initialize services
            using var scope = scopeFactory.CreateScope();
            _encryptionService = scope.ServiceProvider.GetRequiredService<IClientEncryptionService>();
            _secureKeyManager = scope.ServiceProvider.GetRequiredService<ISecureKeyManager>();
            _storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
        }

        protected override async Task<SignInFormViewModel> CreateSelectedItem()
        {
            using var scope = ScopeFactory.CreateScope();
            _storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            var userName = await _storageService.GetValue(ClaimTypes.Name);
            var passkey = string.Empty;
#if DEBUG

            userName = "Jill";
            passkey = "Cancel55%%"; // Default password for debugging
#endif
            var viewModel = new SignInFormViewModel
            {
                NickName = userName,
                PassKey = passkey,
                DeviceString = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}",
                ShowPassword = false
            };

            return await Task.FromResult(viewModel);
        }

        protected override SignInFormBusinessObject ConvertViewModelToBusinessModel(SignInFormViewModel? formViewModel)
        {
            return new SignInFormBusinessObject()
            {
                NickName = formViewModel.NickName?.Trim(),
                DeviceString = formViewModel.DeviceString,
                PassKey = formViewModel.PassKey
            };
        }

        // Focus event handlers removed - using platform default focus behavior

        public override async Task OnAfterSaveAsync(string authResult)
        {
            try
            {
                if (authResult == "Authenticate Online")
                {
                    var scope = ScopeFactory.CreateScope();
                    var signInClientService = scope.ServiceProvider.GetRequiredKeyedService<ISignInFormDataService>("client");
                    var selectedBusinessObject = ConvertViewModelToBusinessModel(SelectedItem);

                    if (!string.IsNullOrEmpty(selectedBusinessObject.NickName))
                        selectedBusinessObject.PassKey = $"{selectedBusinessObject.NickName}{_encryptionService.GenerateBitSignature(SelectedItem.PassKey!)}!";

                    authResult = await signInClientService.SaveAsync(selectedBusinessObject);

                }

                var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(authResult);

                if (authClaims != null)
                {
                    authClaims.Token = authClaims.Token;
                    await StoreAuthenticationTokens(authClaims);
                    await _secureKeyManager.DeriveAndStoreKeysAsync(SelectedItem.NickName!, SelectedItem.PassKey!);

                    // Notify authentication state provider
                    //using var scope = ScopeFactory.CreateScope();
                    //var authStateProvider = scope.ServiceProvider.GetRequiredService<CustomAuthStateProvider>();
                    //authStateProvider.NotifyUserAuthentication(authClaims.UserId, authClaims.Username);

                    await RegisterDevice();
                    await Shell.Current.GoToAsync("//messages");
                }
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
                System.Diagnostics.Debug.WriteLine($"Error processing authentication: {ex.Message}");
            }
        }

        private async Task StoreAuthenticationTokens(AuthorizationClaimsModel authClaims)
        {
            try
            {
                await _storageService.SetValue(authClaims.Token, "auth_token");
                await _storageService.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
                await _storageService.SetValue(authClaims.Username, ClaimTypes.Name);
                await _storageService.SetValue(authClaims.Pub1, "pub1o_");
                await _storageService.SetValue(authClaims.Pub2, "pub2e_");

                // Sync user profile data including avatar
                var scope = ScopeFactory.CreateScope();
                var profileSyncService = scope.ServiceProvider.GetRequiredService<Platform.Client.Services.Features.Account.IProfileSyncService>();
                await profileSyncService.SyncUserProfileAsync(authClaims.UserId, authClaims.Username);

                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
                if (user == null)
                {
                    user = new ApplicationUser()
                    {
                        Id = authClaims.UserId,
                        NickName = authClaims.Username,
                        Hash = "dummy",
                    };
                    context.ApplicationUsers.Add(user);
                }

                user.Pub1 = authClaims.Pub1;
                user.Pub2 = authClaims.Pub2;
                user.DisplayName = authClaims.DisplayName;
                user.AvatarData = authClaims.AvatarData;
                user.AvatarDescription = authClaims.AvatarDescription;
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't throw as authentication was successful
                System.Diagnostics.Debug.WriteLine($"Error storing authentication tokens: {ex.Message}");
            }
        }

        private async Task RegisterDevice()
        {
            var deviceId = await _storageService.GetValue("device_id");
            if (string.IsNullOrEmpty(deviceId))
            {
                deviceId = Guid.NewGuid().ToString();
                await _storageService.SetValue(deviceId, "device_id");
            }

            var deviceToken = await _storageService.GetValue("device_token");
            if (string.IsNullOrEmpty(deviceToken))
            {
                try
                {
                    var scope = ScopeFactory.CreateScope();
                    var fcm = scope.ServiceProvider.GetRequiredService<IFirebaseCloudMessaging>();
                    deviceToken = await fcm.GetTokenAsync();
                    await _storageService.SetValue(deviceToken, "device_token");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error getting FCM token: {ex.Message}");
                    deviceToken = "unavailable";
                }
            }

            var deviceTokenRegistration = await _storageService.GetValue("device_token_registration");
            if (string.IsNullOrEmpty(deviceTokenRegistration))
            {
                try
                {
                    var scope = ScopeFactory.CreateScope();
                    var deviceTokenFormDataService = scope.ServiceProvider.GetRequiredService<IDeviceTokenFormDataService>();
                    await deviceTokenFormDataService.SaveAsync(new DeviceTokenFormBusinessObject()
                    {
                        Id = deviceId,
                        DeviceToken = deviceToken,
                        DeviceName = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}",
                        Platform = DeviceInfo.Platform.ToString(),
                    });

                    await _storageService.SetValue("true", "device_token_registration");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error registering device token: {ex.Message}");
                }
            }
        }

        private void HandleError(string error)
        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                HasError = !string.IsNullOrEmpty(error);
                Error = error;
            });
        }
    }

}
