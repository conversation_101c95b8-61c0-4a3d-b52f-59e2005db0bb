using Microsoft.Extensions.Logging;
using System.Security.Claims;
using DeepMessage.MauiApp.Services;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.Extensions.DependencyInjection;
using Platform.Framework.Core;

namespace Platform.Client.Services.Services;

/// <summary>
/// Result of message decryption attempt
/// </summary>
public class DecryptionResult
{
    public bool Success { get; set; }
    public string Content { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public DecryptionErrorType ErrorType { get; set; }
}

/// <summary>
/// Types of decryption errors for better error handling
/// </summary>
public enum DecryptionErrorType
{
    None,
    UserNotAuthenticated,
    KeyNotFound,
    DecryptionFailed,
    NetworkError,
    InvalidInput
}



/// <summary>
/// Implementation of message decryption service
/// Handles AES key retrieval and message decryption for received messages
/// </summary>
public class MessageDecryptionService
{
    private readonly IEphemeralFormDataService _ephemeralFormService;
    private readonly IAESKeyCacheService _cacheService;
    private readonly IClientEncryptionService _encryptionService;
    private readonly ISecureKeyManager _secureKeyManager;
    private readonly ILocalStorageService _localStorageService;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MessageDecryptionService> _logger;

    public MessageDecryptionService(
        IEphemeralFormDataService ephemeralFormService,
        IAESKeyCacheService cacheService,
        IClientEncryptionService encryptionService,
        ISecureKeyManager secureKeyManager,
        ILocalStorageService localStorageService,
        IServiceProvider serviceProvider,
        ILogger<MessageDecryptionService> logger)
    {
        _ephemeralFormService = ephemeralFormService;
        _cacheService = cacheService;
        _encryptionService = encryptionService;
        _secureKeyManager = secureKeyManager;
        _localStorageService = localStorageService;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public  DecryptionResult  DecryptMessageAsync(string encryptedContent, string friendshipId, bool isCurrentUserSender)
    {
        if (string.IsNullOrEmpty(encryptedContent))
        {
            return new DecryptionResult
            {
                Success = true,
                Content = string.Empty
            };
        }

        _logger.LogDebug("Attempting to decrypt message for friendship {FriendshipId}, isSender: {IsSender}",
            friendshipId, isCurrentUserSender);



        // 2. Try to get AES key
        var aesKey =   GetAESKeyForDecryption(friendshipId, isCurrentUserSender);
        if (aesKey == null)
        {
            return new DecryptionResult
            {
                Success = false,
                ErrorMessage = "AES key not available for this conversation",
                ErrorType = DecryptionErrorType.KeyNotFound
            };
        }

        // 3. Attempt decryption
        try
        {
            var decryptedContent = _encryptionService.DecryptWithAESAsync(encryptedContent, aesKey);
            return new DecryptionResult
            {
                Success = true,
                Content = decryptedContent
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt message for friendship {FriendshipId}", friendshipId);
            return new DecryptionResult
            {
                Success = false,
                ErrorMessage = "Message decryption failed",
                ErrorType = DecryptionErrorType.DecryptionFailed
            };
        }
    }

    public string GenerateFriendshipId(string userId1, string userId2)
    {
        if (string.IsNullOrEmpty(userId1) || string.IsNullOrEmpty(userId2))
            throw new ArgumentException("Both user IDs must be non-null and non-empty");

        if (userId1.Length != userId2.Length)
            throw new ArgumentException("Both user IDs must have the same length");

        string combined = string.Empty;
        for (int i = 0; i < userId1.Length; i++)
        {
            combined += (i % 2 == 0) ? userId1[i] : userId2[i];
        }

        return combined;
    }

    private byte[]? GetAESKeyForDecryption(string friendshipId, bool isCurrentUserSender)
    {
        // 1. Check cache first
        var cachedKey = _cacheService.GetDecryptedAESKeyAsync(friendshipId);
        if (cachedKey != null)
        {
            _logger.LogDebug("Using cached AES key for friendship {FriendshipId}", friendshipId);
            return cachedKey;
        }

        // 2. Try to get from local database
        var friendshipData = Task.Run(() => _ephemeralFormService.GetItemByIdAsync(friendshipId)).Result;
        if (friendshipData?.SenderAESKey == null)
        {
            // 3. Try to fetch from server via client service
            try
            {
                var clientEphemeralService = _serviceProvider.GetRequiredKeyedService<IEphemeralFormDataService>("client");
                friendshipData = Task.Run(() => clientEphemeralService.GetItemByIdAsync(friendshipId)).Result;

                if (friendshipData?.ReceiverAESKey != null)
                {
                    // Save to local database for future use
                    _ = Task.Run(() => _ephemeralFormService.SaveAsync(friendshipData)).Result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch friendship data from server for {FriendshipId}", friendshipId);
            }
        }

        if (friendshipData == null)
        {
            _logger.LogWarning("No friendship data found for {FriendshipId}", friendshipId);
            return null;
        }

        // 4. Decrypt appropriate key based on sender/receiver role
        var encryptedKey = isCurrentUserSender ? friendshipData.SenderAESKey : friendshipData.ReceiverAESKey;
        if (string.IsNullOrEmpty(encryptedKey))
        {
            _logger.LogWarning("No {Role} AES key found for friendship {FriendshipId}",
                isCurrentUserSender ? "sender" : "receiver", friendshipId);
            return null;
        }

        var decryptedKey = DecryptAESKey(encryptedKey);
        if (decryptedKey != null)
        {
            _cacheService.CacheDecryptedAESKeyAsync(friendshipId, decryptedKey);
            _logger.LogDebug("Successfully decrypted and cached AES key for friendship {FriendshipId}", friendshipId);
        }

        return decryptedKey;
    }

    private byte[]? DecryptAESKey(string encryptedKey)
    {
        if (!_secureKeyManager.IsRSAKeyAvailable())
        {
            _logger.LogWarning("RSA key not available for AES key decryption");
            return null;
        }

        try
        {
            using var privateKey = _secureKeyManager.GetRSAPrivateKeyAsync();
            var decryptedKeyBase64 = _encryptionService.DecryptWithRSAPrivateKey(encryptedKey, privateKey);
            return Convert.FromBase64String(decryptedKeyBase64);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt AES key");
            return null;
        }
    }
}
