﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.AuthCodes;
public class AuthCodeServerSideListingDataService : ServerSideListingDataService<AuthCodeListingBusinessObject, AuthCodeFilterBusinessObject>, IAuthCodeListingDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor httpContextAccessor;

    public AuthCodeServerSideListingDataService(AppDbContext context, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        this.httpContextAccessor = httpContextAccessor;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<AuthCodeListingBusinessObject> GetQuery(AuthCodeFilterBusinessObject filterBusinessObject)
    {
        var userId = httpContextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var query = from m in _context.AuthCodes
                    where m.CreatedBy == userId
                    select new AuthCodeListingBusinessObject
                    {
                        Id = m.Id,
                        AuthCode = m.Code,
                        CreatedAt = m.CreatedAt,
                        ExpiresAt = m.ExpiresAt,
                        AuthCodeStatus = m.AuthCodeStatus,
                        ConsumedAt = m.ConsumedAt,
                        ConsumedBy = m.ConsumedBy, // should be encrypted by signal protocol
                    };

        // ✅ IMPROVED UX: Sort by creation date descending (newest first)
        return query.OrderByDescending(x => x.CreatedAt);
    }
}
