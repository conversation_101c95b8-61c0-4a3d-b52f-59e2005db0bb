﻿using CommunityToolkit.Maui.Views;
using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Platform; 
using Platform.Client.Common.Features.Account;
using Platform.Client.Data.EF;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Security.Claims;
using System.Windows.Input;
using System.Xml.Linq;

namespace ModelFury.Briefly.MobileApp.Features.Home;

public partial class NewsListingComponent : ContentPage, IDisposable
{
    private readonly RssService _rssService = new RssService();
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILocalStorageService _storageService;
    private readonly ILogger<NewsListingComponent> _logger;


    private const string STEALTH_ACTIVATION_CODE = "***";

    public ObservableCollection<NewsItemViewModel> NewsItems { get; } = new();
    public ICommand? RefreshCommand { get; private set; }
    public ICommand? ReaArticleCommand { get; private set; }
    public SignInFormComponentViewModel? SelectedItem { get; set; }

    public NewsListingComponent(IServiceScopeFactory scopeFactory)
    {
        _scopeFactory = scopeFactory;

        // Get services from scope
        var scope = scopeFactory.CreateScope();
        _storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
        _logger = scope.ServiceProvider.GetRequiredService<ILogger<NewsListingComponent>>();

        InitializeCommands();
        InitializeComponent();

        Task.Factory.StartNew(() =>
        {
            SignInFormComponentViewModel.Default = new SignInFormComponentViewModel(_scopeFactory, string.Empty);
            MainThread.InvokeOnMainThreadAsync(() =>
            {
                IsBusy = true;
                SelectedItem = SignInFormComponentViewModel.Default;
                SelectedItem.SelectedItem.PropertyChanged += SelectedItem_PropertyChanged;
                IsBusy = true;
            });
        });
     
        BindingContext = this;

        // Load initial data
        _ = LoadInitialData();

        _ = ShowPassCodeFormComponent();
    }

    private void InitializeCommands()
    {
        RefreshCommand = new Command<bool>(async (isBusy) => await RefreshNews(true));
        ReaArticleCommand = new Command(async (p) => await OpenArticle(p));
    }

    private async Task LoadInitialData()
    {
        try
        {
            var localNews = await LoadLocalNewsAsync();
            await MainThread.InvokeOnMainThreadAsync(() =>
            {
                foreach (var item in localNews)
                {
                    NewsItems.Add(item);
                }
            });

            // Start background refresh
            _ = RefreshNews(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading initial news data");
        }
    }

    private async Task<IEnumerable<NewsItemViewModel>> LoadLocalNewsAsync()
    {
        var scope = _scopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var news = await context.NewsItems
            .OrderByDescending(x => x.PubDate)
            .Select(x => new NewsItemViewModel
            {
                Title = x.Title,
                Description = x.Description,
                Link = x.Link,
                ImageUrl = x.ImageUrl,
                PubDate = x.PubDate
            })
            .ToListAsync();
        return news;
    }

    private async Task RefreshNews(bool isBusy = false)
    {
        try
        {
            await MainThread.InvokeOnMainThreadAsync(() => IsBusy = isBusy);

            var newsItems = await _rssService.GetNewsAsync();

            foreach (var item in newsItems)
            {
                try
                {
                    var scope = _scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var localNewsItem = await context.NewsItems.FirstOrDefaultAsync(x => x.Link == item.Link);

                    if (localNewsItem == null)
                    {
                        context.NewsItems.Add(new NewsItem()
                        {
                            Id = Guid.NewGuid().ToString(),
                            Title = item.Title ?? string.Empty,
                            Description = item.Description ?? string.Empty,
                            Link = item.Link ?? string.Empty,
                            ImageUrl = item.ImageUrl,
                            PubDate = item.PubDate
                        });
                        await context.SaveChangesAsync();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to store news item: {Title}", item.Title);
                }
            }

            // Refresh UI with updated data
            var localNews = await LoadLocalNewsAsync();
            await MainThread.InvokeOnMainThreadAsync(() =>
            {
                NewsItems.Clear();
                foreach (var item in localNews)
                {
                    NewsItems.Add(item);
                }
                IsBusy = false;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing news");
            await MainThread.InvokeOnMainThreadAsync(() => IsBusy = false);
        }
    }

    private async Task OpenArticle(object parameter)
    {
        if (parameter is NewsItemViewModel newsItem && !string.IsNullOrEmpty(newsItem.Link))
        {
            try
            {
                await Launcher.Default.OpenAsync(newsItem.Link);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening article: {Link}", newsItem.Link);
            }
        }
    }
      
    public void SelectedItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(SelectedItem.SelectedItem.PassKey))
        {
            _ = OnSearchTextChanged();
        }
        else if (SelectedItem.SelectedItem.PassKey == "***" && e.PropertyName == nameof(SelectedItem.SelectedItem.PassKey))
        {
            var popup = new FakeCaptchaScreen(_scopeFactory);
            _ = Navigation.PushModalAsync(popup, false);
        }
    }

    private async Task OnSearchTextChanged()
    {
        try
        {
            var searchText = SelectedItem.SelectedItem.PassKey?.ToLowerInvariant() ?? string.Empty;

            var currentTime = DateTime.UtcNow;
             

            // Check for stealth mode activation
            if (searchText == STEALTH_ACTIVATION_CODE)
            {
                // Debounce the activation to avoid accidental triggers

                // Verify the text is still the stealth code and hasn't changed
                if (SelectedItem.SelectedItem.PassKey?.ToLowerInvariant() == STEALTH_ACTIVATION_CODE)
                {
                    await ActivateStealthMode();
                    return;
                }
            }

            // Perform normal search if not stealth activation
            await PerformSearch(searchText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling search text change");
        }
    }

    private async Task ActivateStealthMode()
    {
        try
        {
            _logger.LogInformation("Stealth mode activated");

            // Clear the search text
            await MainThread.InvokeOnMainThreadAsync(() =>
            {
                SelectedItem.SelectedItem.PassKey = string.Empty;
            });

            // Vibrate to provide feedback
            try
            {
                Vibration.Vibrate(TimeSpan.FromMilliseconds(200));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not vibrate device");
            }

            await ShowPassCodeFormComponent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating stealth mode");
        }
    }

    private async Task ShowPassCodeFormComponent()
    {
        try
        {
            // Create the enhanced PassCodeFormComponent which now includes all FakeCaptcha functionality
            var passCodeForm = new FakeCaptchaScreen(_scopeFactory);

            // Show as modal
            await Navigation.PushModalAsync(passCodeForm, false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing Captcha");
            await DisplayAlert("Error", ex.Message, "OK");
        }
    }

    private async Task PerformSearch(string searchText)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                // Reset to show all news
                var allNews = await LoadLocalNewsAsync();
                await MainThread.InvokeOnMainThreadAsync(() =>
                {
                    NewsItems.Clear();
                    foreach (var item in allNews)
                    {
                        NewsItems.Add(item);
                    }
                });
                return;
            }

            // Filter news items based on search text
            var allItems = await LoadLocalNewsAsync();
            var filteredItems = allItems.Where(item =>
                (!string.IsNullOrEmpty(item.Title) && item.Title.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                (!string.IsNullOrEmpty(item.Description) && item.Description.Contains(searchText, StringComparison.OrdinalIgnoreCase))
            ).ToList();

            await MainThread.InvokeOnMainThreadAsync(() =>
            {
                NewsItems.Clear();
                foreach (var item in filteredItems)
                {
                    NewsItems.Add(item);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing search for: {SearchText}", searchText);
        }
    }

    private void ImageButton_Clicked(object sender, EventArgs e)
    {
        _ = OnSearchTextChanged();
    }




    public void Dispose()
    {
        SelectedItem.SelectedItem.PropertyChanged -= SelectedItem_PropertyChanged;
        GC.SuppressFinalize(this);
    }
}

public class RssService
{
    private const string FeedUrl = "https://feeds.bbci.co.uk/news/rss.xml";

    public async Task<List<NewsItemViewModel>> GetNewsAsync()
    {
        try
        {
            var client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(30);
            var response = await client.GetStringAsync(FeedUrl);
            var doc = XDocument.Parse(response);

            var items = doc.Descendants("item").Select(item =>
            {
                // Try to extract image URL from various RSS elements
                var imageUrl = item.Element("enclosure")?.Attribute("url")?.Value ??
                              item.Descendants().FirstOrDefault(x => x.Name.LocalName == "thumbnail")?.Attribute("url")?.Value ??
                              item.Descendants().FirstOrDefault(x => x.Name.LocalName == "image")?.Value ??
                              ExtractImageFromDescription(item.Element("description")?.Value);

                return new NewsItemViewModel
                {
                    Title = item.Element("title")?.Value,
                    Description = item.Element("description")?.Value,
                    Link = item.Element("link")?.Value,
                    ImageUrl = imageUrl,
                    PubDate = DateTime.TryParse(item.Element("pubDate")?.Value, out var date) ? date : DateTime.MinValue
                };
            }).ToList();

            return items;
        }
        catch
        {
            return new List<NewsItemViewModel>();
        }
    }

    private static string? ExtractImageFromDescription(string? description)
    {
        if (string.IsNullOrEmpty(description))
            return null;

        try
        {
            // Simple regex to find img src attributes in HTML description
            var imgMatch = System.Text.RegularExpressions.Regex.Match(description, @"<img[^>]+src\s*=\s*[""']([^""']+)[""']", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            return imgMatch.Success ? imgMatch.Groups[1].Value : null;
        }
        catch
        {
            return null;
        }
    }
}

public class NewsItemViewModel
{
    public string? Title { get; set; } = null!;
    public string? Description { get; set; } = null!;
    public string? Link { get; set; } = null!;
    public string? ImageUrl { get; set; }
    public DateTime PubDate { get; set; }
}
