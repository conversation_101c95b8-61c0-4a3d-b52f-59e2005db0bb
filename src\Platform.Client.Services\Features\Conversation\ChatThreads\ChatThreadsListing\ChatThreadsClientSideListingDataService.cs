﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.MauiApp.Helpers;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.Client.Common.Data;
using System.Security.Claims;
using DeepMessage.Cient.Common.Data;
using Platform.Framework.Core;
namespace Platform.Client.Services.Features.Conversation;
public class ChatThreadsOfflineListingDataService : ClientSideListingDataService<ChatThreadsListingBusinessObject, ChatThreadsFilterBusinessObject>, IChatThreadsListingDataService
{

    private readonly AppDbContext _context;
    private readonly ILocalStorageService localStorageService;

    public ChatThreadsOfflineListingDataService(AppDbContext context, ILocalStorageService localStorageService)
    {
        _context = context;
        this.localStorageService = localStorageService;
    }

    public override IQueryable<ChatThreadsListingBusinessObject> GetQuery(ChatThreadsFilterBusinessObject filterBusinessObject)
    {
        var userId = Task.Run(() => (localStorageService.GetValue(ClaimTypes.NameIdentifier))).Result;
        var conversations = (from c in _context.Conversations
                             from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                             where p.UserId == userId
                             select c.Id).ToList();
   
        var friendships = (from f in _context.Friendships select f).ToList();
        var conversationParticipants = (from p in _context.ConversationParticipants select p).ToList();

        var query = (from c in _context.Conversations
                     from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                     from f in _context.Friendships.Where(x => x.FriendId == p.UserId && x.UserId == userId)
                     where conversations.Contains(c.Id) && p.UserId != userId
                     select new ChatThreadsListingBusinessObject
                     {
                         Id = c.Id,
                         Avatar = f.AvatarData,
                         Name = f.Name,
                         LastMessage = (from m in _context.Messages
                                        from r in _context.MessageRecipients.Where(r => r.MessageId == m.Id && r.RecipientId == userId)
                                        where m.ConversationId == c.Id
                                        orderby m.CreatedAt descending
                                        select r.EncryptedContent)
                             .FirstOrDefault(),
                         LastMessageTime = _context.Messages.Where(x => x.ConversationId == c.Id)
                             .OrderByDescending(x => x.CreatedAt)
                             .Select(x => x.CreatedAt)
                             .FirstOrDefault()
                     });

        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            query = query.Where(thread =>
                thread.Name.ToLower().Contains(searchTerm) ||
                (thread.LastMessage != null && thread.LastMessage.ToLower().Contains(searchTerm))
            );
        }

        // Order by last message time (most recent first)
        return query.OrderByDescending(thread => thread.LastMessageTime);
    }
}

public class ChatThreadsClientSideListingDataService : IChatThreadsListingDataService
{

    private readonly BaseHttpClient _httpClient;

    public ChatThreadsClientSideListingDataService(BaseHttpClient context)
    {
        _httpClient = context;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<PagedDataList<ChatThreadsListingBusinessObject>> GetPaginatedItems(ChatThreadsFilterBusinessObject filterBusinessObject)
    {
        return await _httpClient.GetFromJsonAsync<PagedDataList<ChatThreadsListingBusinessObject>>($"api/ChatThreadsListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
    }
}

