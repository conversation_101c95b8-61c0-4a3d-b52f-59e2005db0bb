﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace DeepMessage.Server.DataServices.Data
{
    public class AppDbContext : IdentityDbContext<ApplicationUser>
    {
        public AppDbContext(DbContextOptions<AppDbContext> options)
            : base(options)
        { 
        }

        public DbSet<AuthCode> AuthCodes { get; set; }
        public DbSet<CacheEntry> CacheEntries => Set<CacheEntry>();
        public DbSet<Conversation> Conversations => Set<Conversation>();
        public DbSet<ConversationParticipant> ConversationParticipants => Set<ConversationParticipant>();
        public DbSet<Message> Messages => Set<Message>();
        public DbSet<MessageRecipient> MessageRecipients => Set<MessageRecipient>();
        public DbSet<MessageAttachment> MessageAttachments => Set<MessageAttachment>();

        public DbSet<FriendRequest> FriendRequests => Set<FriendRequest>();
        public DbSet<Friendship> Friendships => Set<Friendship>();

        public DbSet<UserDevice> UserDevices => Set<UserDevice>();
        public DbSet<Image> Images => Set<Image>();
        public DbSet<NewsItem> NewsItems => Set<NewsItem>();

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
             

            // User -> MessageRecipient (one-to-many)
            modelBuilder.Entity<MessageRecipient>()
                .HasOne(mr => mr.Recipient)
                .WithMany(u => u.MessageRecipients)
                .HasForeignKey(mr => mr.RecipientId)
                .OnDelete(DeleteBehavior.Restrict);

            // Message -> MessageRecipient (one-to-many)
            modelBuilder.Entity<MessageRecipient>()
                .HasOne(mr => mr.Message)
                .WithMany(m => m.Recipients)
                .HasForeignKey(mr => mr.MessageId);

            // Conversation -> Messages (one-to-many)
            modelBuilder.Entity<Message>()
                .HasOne(m => m.Conversation)
                .WithMany(c => c.Messages)
                .HasForeignKey(m => m.ConversationId);

            // Message -> Attachments (one-to-many)
            modelBuilder.Entity<MessageAttachment>()
                .HasOne(ma => ma.Message)
                .WithMany(m => m.Attachments)
                .HasForeignKey(ma => ma.MessageId);

            // Sender relationship
            modelBuilder.Entity<Message>()
                .HasOne(m => m.Sender)
                .WithMany()
                .HasForeignKey(m => m.SenderId)
                .OnDelete(DeleteBehavior.Restrict);

            // Participant relationship
            modelBuilder.Entity<ConversationParticipant>()
                .HasOne(cp => cp.Conversation)
                .WithMany(c => c.Participants)
                .HasForeignKey(cp => cp.ConversationId);

            modelBuilder.Entity<ConversationParticipant>()
                .HasOne(cp => cp.User)
                .WithMany(u => u.Conversations)
                .HasForeignKey(cp => cp.UserId);

            // Prevent duplicate participants in a conversation:
            modelBuilder.Entity<ConversationParticipant>()
                .HasIndex(cp => new { cp.ConversationId, cp.UserId })
                .IsUnique();

            // Likewise, you might want a unique constraint on 
            // (MessageId, RecipientId) in MessageRecipient, 
            // so you don't accidentally insert duplicates:
            modelBuilder.Entity<MessageRecipient>()
                .HasIndex(mr => new { mr.MessageId, mr.RecipientId })
                .IsUnique();
             
            modelBuilder.Entity<FriendRequest>()
            .HasOne(fr => fr.User)
            .WithMany(u => u.FriendRequestsSent)
            .HasForeignKey(fr => fr.UserId)
            .OnDelete(DeleteBehavior.Restrict);
              
            // Friendships
            modelBuilder.Entity<Friendship>()
                .HasOne(f => f.User)
                .WithMany(u => u.Friendships)
                .HasForeignKey(f => f.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Friendship>()
                .HasOne(f => f.Friend)
                .WithMany()
                .HasForeignKey(f => f.FriendId)
                .OnDelete(DeleteBehavior.Restrict);

            // If you want a unique (UserId, FriendId) so you don't duplicate:
            modelBuilder.Entity<Friendship>()
                .HasIndex(f => new { f.UserId, f.FriendId })
                .IsUnique();
        }

    }
}
