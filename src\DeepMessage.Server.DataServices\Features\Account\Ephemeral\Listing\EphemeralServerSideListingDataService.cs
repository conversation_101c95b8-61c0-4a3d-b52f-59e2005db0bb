﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.Account;

public class EphemeralServerSideListingDataService : ServerSideListingDataService<EphemeralListingBusinessObject, EphemeralFilterBusinessObject>, IEphemeralListingDataService
{
    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor _contextAccessor;

    public EphemeralServerSideListingDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        _contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<EphemeralListingBusinessObject> GetQuery(EphemeralFilterBusinessObject filterBusinessObject)
    {
        var userId = _contextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        var query = from f in _context.Friendships
                    where f.UserId == userId
                    select new EphemeralListingBusinessObject
                    {
                        FriendshipId = f.Id,
                        SenderAESKey = f.SenderAESKey,
                        ReceiverAESKey = f.ReceiverAESKey,
                        AESKeyActive = f.AESKeyActive,
                        AESKeyCreatedAt = f.AESKeyCreatedAt,
                        UserId = f.UserId,
                        FriendId = f.FriendId
                    };

        // Apply SearchKey filter for specific friendship ID lookup
        if (!string.IsNullOrEmpty(filterBusinessObject.SearchKey))
        {
            query = query.Where(f => f.FriendshipId == filterBusinessObject.SearchKey);
        }

        // Apply additional filters
        if (!string.IsNullOrEmpty(filterBusinessObject.UserId))
        {
            query = query.Where(f => f.UserId == filterBusinessObject.UserId);
        }

        if (!string.IsNullOrEmpty(filterBusinessObject.FriendId))
        {
            query = query.Where(f => f.FriendId == filterBusinessObject.FriendId);
        }

        if (filterBusinessObject.AESKeyActive.HasValue)
        {
            query = query.Where(f => f.AESKeyActive == filterBusinessObject.AESKeyActive.Value);
        }

        return query;
    }
}
