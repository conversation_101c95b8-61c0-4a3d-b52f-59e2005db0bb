using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.Client.Common.Data;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using DeepMessage.MauiApp.Services;
using Platform.Framework.Core;

namespace Platform.Client.Services.Features.Account;

/// <summary>
/// Offline-first client-side listing service for AES key management
/// Queries local SQLite database for friendship AES key information
/// </summary>
public class EphemeralOfflineListingDataService : IEphemeralListingDataService
{
    private readonly AppDbContext _context;
    private readonly ILocalStorageService _localStorageService;

    public EphemeralOfflineListingDataService(AppDbContext context, ILocalStorageService localStorageService)
    {
        _context = context;
        _localStorageService = localStorageService;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<PagedDataList<EphemeralListingBusinessObject>> GetPaginatedItems(EphemeralFilterBusinessObject filterBusinessObject)
    {
       

        var query = from f in _context.Friendships
                    select new EphemeralListingBusinessObject
                    {
                        FriendshipId = f.Id,
                        SenderAESKey = f.SenderAESKey,
                        ReceiverAESKey = f.ReceiverAESKey,
                        AESKeyActive = f.AESKeyActive,
                        AESKeyCreatedAt = f.AESKeyCreatedAt,
                        UserId = f.UserId,
                        FriendId = f.FriendId
                    };

        // Apply SearchKey filter for specific friendship ID lookup
        if (!string.IsNullOrEmpty(filterBusinessObject.SearchKey))
        {
            query = query.Where(f => f.FriendshipId == filterBusinessObject.SearchKey);
        }

        // Apply additional filters
        if (!string.IsNullOrEmpty(filterBusinessObject.UserId))
        {
            query = query.Where(f => f.UserId == filterBusinessObject.UserId);
        }

        if (!string.IsNullOrEmpty(filterBusinessObject.FriendId))
        {
            query = query.Where(f => f.FriendId == filterBusinessObject.FriendId);
        }

        if (filterBusinessObject.AESKeyActive.HasValue)
        {
            query = query.Where(f => f.AESKeyActive == filterBusinessObject.AESKeyActive.Value);
        }

        // Get total count
        var totalRows = await query.CountAsync();

        // Apply pagination
        if (filterBusinessObject.UsePagination)
        {
            var skip = (filterBusinessObject.CurrentIndex - 1) * filterBusinessObject.RowsPerPage;
            query = query.Skip(skip).Take(filterBusinessObject.RowsPerPage);
        }

        var items = await query.ToListAsync();
        var totalPages = filterBusinessObject.UsePagination 
            ? (int)Math.Ceiling((double)totalRows / filterBusinessObject.RowsPerPage)
            : 1;

        return new PagedDataList<EphemeralListingBusinessObject>
        {
            Items = items,
            TotalRows = totalRows,
            TotalPages = totalPages
        };
    }
}
