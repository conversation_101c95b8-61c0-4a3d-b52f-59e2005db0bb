﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using Platform.Client.Common.Features.AuthCodes;
using System.Security.Claims;
using System.Windows.Input;
using Platform.Framework.Core;
using Platform.Client.Services.Features.Account;

namespace Platform.Client.Common.Features.Account;

public class ProfileListingViewBase : ListingBaseMaui<ProfileListingViewModel, ProfileListingBusinessObject,
                                        ProfileFilterViewModel, ProfileFilterBusinessObject, IProfileListingDataService>
{
    public ProfileListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}

public partial class ProfileListingView : ProfileListingViewBase
{
    private ProfileListingViewModel _item = new ProfileListingViewModel();
    public ProfileListingViewModel Item
    {
        get => _item;
        set
        {
            _item = value;
            OnPropertyChanged();
        }
    }


    // Commands
    public ICommand UpdateProfileCommand { get; }
    public ICommand ChangePasswordCommand { get; }
    public ICommand NavigateToReferralCodesCommand { get; }
    public ICommand LogoutCommand { get; }
    public ICommand RefreshCommand { get; }


    public ProfileListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        InitializeComponent();
        
        // Initialize commands
        UpdateProfileCommand = new Command(async () => await UpdateProfile());
        ChangePasswordCommand = new Command(async () => await ChangePassword());
        NavigateToReferralCodesCommand = new Command(async () => await NavigateToReferralCodes());
        LogoutCommand = new Command(async () => await Logout());
        RefreshCommand = new Command(async () => await LoadItems());
        BindingContext = this;
    }

    protected override Task ItemsLoaded(IProfileListingDataService service)
    {
        if (Items != null && Items.Count > 0)
        {
            var firstItem = Items.First();
            Item.Id = firstItem.Id;
            Item.NickName = firstItem.NickName;
            Item.DisplayName = firstItem.DisplayName;
            Item.AvatarData = firstItem.AvatarData;

        }
        return base.ItemsLoaded(service);
    }

    /// <summary>
    /// Navigates to update profile page
    /// </summary>
    private async Task UpdateProfile()
    {
        try
        {
            using var scope = ScopeFactory.CreateScope();
            var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            var userId = await storageService.GetValue(ClaimTypes.NameIdentifier);

            await Navigation.PushModalAsync(new ProfileFormView(ScopeFactory, userId));

        }
        catch (Exception ex)
        {
            HandleError($"Failed to navigate to profile edit: {ex.Message}");
        }
    }

    /// <summary>
    /// Navigates to change password page
    /// </summary>
    private async Task ChangePassword()
    {
        try
        {
            await Shell.Current.GoToAsync("//change-password");
        }
        catch (Exception ex)
        {
            HandleError($"Failed to navigate to change password: {ex.Message}");
        }
    }

    /// <summary>
    /// Navigates to referral codes page
    /// </summary>
    private async Task NavigateToReferralCodes()
    {
        try
        {
            await Navigation.PushAsync(new AuthCodeListingView(ScopeFactory));
        }
        catch (Exception ex)
        {
            HandleError($"Failed to navigate to referral codes: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles user logout
    /// </summary>
    private async Task Logout()
    {
        try
        {
            var result = await DisplayAlert("Logout", "Are you sure you want to logout?", "Yes", "No");
            if (result)
            {
                await PerformLogout();
            }
        }
        catch (Exception ex)
        {
            HandleError($"Failed to logout: {ex.Message}");
        }
    }

    /// <summary>
    /// Performs the actual logout operation
    /// </summary>
    private async Task PerformLogout()
    {
        try
        {
            using var scope = ScopeFactory.CreateScope();
            var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

            // Clear authentication tokens
            await storageService.RemoveValue("auth_token");
            await storageService.RemoveValue("refresh_token");
            await storageService.RemoveValue(ClaimTypes.NameIdentifier);
            await storageService.RemoveValue(ClaimTypes.Name);
            await storageService.RemoveValue("pub1o_");
            await storageService.RemoveValue("pub2e_");

            // Clear device registration
            await storageService.RemoveValue("device_token_registration");

            // Notify authentication state provider
            //var authStateProvider = scope.ServiceProvider.GetRequiredService<CustomAuthStateProvider>();
            //authStateProvider.NotifyUserLogout();

            // Navigate to signin
            await Shell.Current.GoToAsync("//signin");
            await Navigation.PopToRootAsync();
        }
        catch (Exception ex)
        {
            HandleError($"Failed to complete logout: {ex.Message}");
        }
    }

    private void HandleError(string error)
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            await DisplayAlert("Error", error, "OK");
        });
    }
}


