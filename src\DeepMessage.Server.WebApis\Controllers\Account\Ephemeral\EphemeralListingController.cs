﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
namespace DeepMessage.Server.WebApis.Controller.Account;
[ApiController, Route("api/[controller]/[action]")]
public class EphemeralListingController : ControllerBase, IEphemeralListingDataService
{

	private readonly IEphemeralListingDataService dataService;

	public EphemeralListingController(IEphemeralListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<PagedDataList<EphemeralListingBusinessObject>> GetPaginatedItems([FromQuery] EphemeralFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
