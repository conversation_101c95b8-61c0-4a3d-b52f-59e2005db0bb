﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
using System.Security.Claims;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiApp.Services;
using Microsoft.EntityFrameworkCore;
using Platform.Framework.Core;
using Platform.Client.Services.Services;
namespace Platform.Client.Services.Features.Conversation;
public class ChatMessageClientSideFormDataService : IChatMessageFormDataService
{

    private readonly BaseHttpClient _httpClient;
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly ChatSyncUpService chatSyncUpService;
    private readonly IClientEncryptionService encryptionService;
    private readonly MessageEncryptionService messageEncryptionService;

    public ChatMessageClientSideFormDataService(BaseHttpClient httpClient, AppDbContext context,
        ILocalStorageService localStorageService, ChatSyncUpService chatThreadsSyncService,
        IClientEncryptionService encryptionService, MessageEncryptionService messageEncryptionService)
    {
        _httpClient = httpClient;
        this.context = context;
        this.localStorageService = localStorageService;
        this.chatSyncUpService = chatThreadsSyncService;
        this.encryptionService = encryptionService;
        this.messageEncryptionService = messageEncryptionService;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatMessageFormBusinessObject formBusinessObject)
    {

        var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.ConversationId);
        ArgumentException.ThrowIfNullOrEmpty(userId);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.Content);

        var trx = await context.Database.BeginTransactionAsync();
        try
        {
            // Create message with NO plaintext content for E2E encryption
            var message = new Message()
            {
                Id = Guid.CreateVersion7().ToString(),
                ConversationId = formBusinessObject.ConversationId,
                CreatedAt = DateTime.UtcNow,
                DeliveryStatus = DeepMessage.ServiceContracts.Enums.MessageDeliveryStatus.QueuedToUpSync,
                SenderId = userId
            };

            context.Messages.Add(message);
            await context.SaveChangesAsync();

            // Get conversation participants with their public keys
            var friends = await (from cp in context.ConversationParticipants
                                 from f in context.Friendships.Where(x => x.FriendId == cp.UserId)
                                 where cp.ConversationId == message.ConversationId
                                 && f.UserId == userId
                                 select new ParticipantInfo
                                 {
                                     UserId = f.UserId,
                                     FriendId = f.FriendId,
                                     Pub1 = f.Pub1, // Friend's public key
                                     IsSender = cp.UserId == userId
                                 }).ToListAsync();

 
             
            // Create encrypted MessageRecipient records for each participant using AES encryption
            foreach (var participant in  friends)
            {
                // Generate friendship ID for AES key lookup
                var friendshipId = messageEncryptionService.GenerateFriendshipId(participant.UserId, participant.FriendId);

                // Get or create AES key for this friendship (always succeeds)
                var aesKey = await messageEncryptionService.GetOrCreateEncryptionKeyAsync(friendshipId, participant.UserId, participant.FriendId);

                // Encrypt message content with AES key
                var encryptedContent = encryptionService.EncryptWithAESAsync(formBusinessObject.Content, aesKey);

                var messageRecipient = new MessageRecipient()
                {
                    Id = Guid.CreateVersion7().ToString().ToLower(),
                    MessageId = message.Id,
                    RecipientId = participant.FriendId,
                    EncryptedContent = encryptedContent,
                    IsRead = false,
                };

                context.MessageRecipients.Add(messageRecipient);
            }

            await context.SaveChangesAsync();
            chatSyncUpService.Sync(new ChatSyncItem() { Id = message.Id, SyncType = SyncType.ChatMessage });
            await trx.CommitAsync();
            return message.Id;
        }
        catch (Exception ex)
        {
            await trx.RollbackAsync();
            throw new InvalidOperationException("Failed to Send chat message", ex);
        }
        finally
        {
            await trx.DisposeAsync();
        }
    }

    /// <summary>
    /// Generates a deterministic friendship ID by combining two user IDs
    /// </summary>
    /// <param name="guid1">First user ID</param>
    /// <param name="guid2">Second user ID</param>
    /// <returns>Combined friendship ID</returns>
    private string CombineGuidsXor(string guid1, string guid2)
    {
        string combined = string.Empty;
        for (int i = 0; i < guid1.Length; i++)
        {
            combined += (i % 2 == 0) ? guid1[i] : guid2[i];
        }
        return combined;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ChatMessageFormBusinessObject> GetItemByIdAsync(string id)
    {
        return await _httpClient.GetFromJsonAsync<ChatMessageFormBusinessObject>($"api/ChatMessageForm/GetItemById?id=" + id);

    }

    public class ParticipantInfo
    {
        public string UserId { get; set; }
        public string FriendId { get; set; }
        public string Pub1 { get; set; }
        public bool IsSender { get; set; }
    }
}
