﻿using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Security;
namespace DeepMessage.Server.DataServices.Features.Friends;

public class FriendsServerSideListingDataService :
        ServerSideListingDataService<FriendsListingBusinessObject, FriendsFilterBusinessObject>, IFriendsListingDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public FriendsServerSideListingDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<FriendsListingBusinessObject> GetQuery(FriendsFilterBusinessObject filterBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        var query = (from f in _context.Friendships
                     join u in _context.Users on f.FriendId equals u.Id
                     where f.UserId == userId || f.FriendId == userId
                     select new FriendsListingBusinessObject
                     {
                         Id = f.Id,
                         UserId = f.UserId,
                         FriendId = f.FriendId,
                         AvatarData = f.AvatarData,
                         TagLine = f.TagLine,
                         Name = f.Name,
                         Status = "Online", // You can enhance this with actual status logic
                         Pub1 = ValidatePublicKey(u.Pub1) // Friend's RSA public key for message encryption with security validation
                     });

        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.Trim();

            // Check if SearchKey is a GUID (friend ID lookup)
            if (Guid.TryParse(searchTerm, out var guidValue))
            {
                // ✅ FIXED: Use case-insensitive GUID comparison for friend ID lookup
                var guidString = guidValue.ToString().ToLower();
                query = query.Where(friend => friend.FriendId.ToLower() == guidString);
            }
            else
            {
                // Standard text search on friend name
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(friend =>
                    friend.Name.ToLower().Contains(searchTermLower)
                );
            }
        }

        // Order by name for consistent results
        return query.OrderBy(friend => friend.Name);
    }

    /// <summary>
    /// Security validation method to ensure only public keys are shared
    /// </summary>
    /// <param name="publicKey">The public key to validate</param>
    /// <returns>Validated public key</returns>
    /// <exception cref="SecurityException">Thrown if private key data is detected</exception>
    private static string ValidatePublicKey(string publicKey)
    {
        if (string.IsNullOrEmpty(publicKey))
        {
            return string.Empty;
        }

        // Security check: Ensure we're not accidentally exposing private key data
        if (publicKey.Contains("PRIVATE") || publicKey.Contains("-----BEGIN RSA PRIVATE KEY-----"))
        {
            throw new SecurityException("Security violation: Attempted to share private key data in friends listing");
        }

        return publicKey;
    }
}
