﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Home;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DeepMessage.Server.DataServices.Features.Home;

public class NewsServerSideListingDataService : ServerSideListingDataService<NewsListingBusinessObject, NewsFilterBusinessObject>, INewsListingDataService
{
    private readonly AppDbContext _context;
    private readonly ILogger<NewsServerSideListingDataService> _logger;

    public NewsServerSideListingDataService(AppDbContext context, ILogger<NewsServerSideListingDataService> logger)
    {
        _context = context;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<NewsListingBusinessObject> GetQuery(NewsFilterBusinessObject filterBusinessObject)
    {
        try
        {
            // First, try to get news from database cache
            var cachedNews = GetCachedNewsQuery();

            // If we have cached news, use it
            if (cachedNews.Any())
            {
                _logger.LogDebug("Serving news from database cache");

                if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
                {
                    var searchTerm = filterBusinessObject.SearchKey.ToLower();
                    cachedNews = cachedNews.Where(item =>
                        (item.Title != null && item.Title.ToLower().Contains(searchTerm)) ||
                        (item.Description != null && item.Description.ToLower().Contains(searchTerm))
                    );
                }

                return cachedNews.OrderByDescending(item => item.PubDate);
            }

            // Fallback to RSS feed if no cached news (this should rarely happen due to background service)
            _logger.LogWarning("No cached news found, falling back to RSS feed");
            var rssNews = RssService.GetNewsAsync().GetAwaiter().GetResult().AsQueryable();

            if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
            {
                var searchTerm = filterBusinessObject.SearchKey.ToLower();
                rssNews = rssNews.Where(item =>
                    (item.Title != null && item.Title.ToLower().Contains(searchTerm)) ||
                    (item.Description != null && item.Description.ToLower().Contains(searchTerm))
                );
            }

            return rssNews.OrderByDescending(item => item.PubDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting news query");
            // Return empty queryable on error
            return new List<NewsListingBusinessObject>().AsQueryable();
        }
    }

    /// <summary>
    /// Gets cached news from database
    /// </summary>
    private IQueryable<NewsListingBusinessObject> GetCachedNewsQuery()
    {
        return _context.NewsItems
            .Where(n => n.IsActive)
            .OrderByDescending(n => n.PubDate)
            .Select(n => new NewsListingBusinessObject
            {
                Title = n.Title,
                Description = n.Description,
                Link = n.Link,
                Thumbnail = n.Thumbnail,
                PubDate = n.PubDate
            });
    }

    public class RssService
    {
        private const string FeedUrl = "https://feeds.bbci.co.uk/news/rss.xml";
        private static readonly XNamespace MediaNamespace = "http://search.yahoo.com/mrss/";

        public static async Task<List<NewsListingBusinessObject>> GetNewsAsync()
        {
            try
            {
                var client = new HttpClient();
                var response = await client.GetStringAsync(FeedUrl);
                var doc = XDocument.Parse(response);

                var items = doc.Descendants("item").Select(item => new NewsListingBusinessObject
                {
                    Title = item.Element("title")?.Value,
                    Description = item.Element("description")?.Value,
                    Link = item.Element("link")?.Value,
                    Thumbnail = item.Element(MediaNamespace + "thumbnail")?.Attribute("url")?.Value,
                    PubDate = DateTime.TryParse(item.Element("pubDate")?.Value, out var date) ? date : DateTime.MinValue
                }).ToList();

                return items;
            }
            catch
            {
                return new List<NewsListingBusinessObject>();
            }
        }
    }
}
