﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatThreadSyncServerSideFormDataService : IChatThreadSyncFormDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatThreadSyncServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatThreadSyncFormBusinessObject formBusinessObject)
    {
        var conversation = await _context.Conversations.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);

        if (conversation == null)
        {
            conversation = new Data.Conversation
            {
                Id = formBusinessObject.Id,
                CreatedAt = formBusinessObject.CreatedAt,
                IsDeleted = formBusinessObject.IsDeleted,
                Type = formBusinessObject.Type,
                Title = formBusinessObject.Title

            };
            _context.Conversations.Add(conversation);
            await _context.SaveChangesAsync();
        }

        ArgumentNullException.ThrowIfNull(formBusinessObject.ChatParticipents);

        foreach (var item in formBusinessObject.ChatParticipents)
        {
            var participants = await _context.ConversationParticipants.FirstOrDefaultAsync(x => x.ConversationId == item.ConversationId
                    && x.UserId == item.UserId);
            if (participants == null)
            {
                participants = new ConversationParticipant()
                {
                    Id = item.Id,
                    ConversationId = item.ConversationId,
                    IsAdmin = item.IsAdmin,
                    JoinedAt = item.JoinedAt,
                    UserId = item.UserId,
                };
                _context.ConversationParticipants.Add(participants);
                await _context.SaveChangesAsync();
            }
        }

        return conversation.Id;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ChatThreadSyncFormBusinessObject> GetItemByIdAsync(string id)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        // ✅ SECURITY: Check if user is a participant in this conversation
        var isParticipant = await _context.ConversationParticipants
            .AnyAsync(cp => cp.ConversationId == id && cp.UserId == userId);

        if (!isParticipant)
        {
            throw new UnauthorizedAccessException($"User does not have access to conversation {id}");
        }

        var conversation = await(from c in _context.Conversations
                                  where c.Id == id
                                  select new ChatThreadSyncFormBusinessObject()
                                  {
                                      Id = c.Id,
                                      CreatedAt = c.CreatedAt,
                                      IsDeleted = c.IsDeleted,
                                      Title = c.Title,
                                      Type = c.Type,
                                      ChatParticipents = (from p in _context.ConversationParticipants
                                                          where p.ConversationId == c.Id
                                                          select new ChatParticipentsSyncFormBusinessObject()
                                                          {
                                                              Id = p.Id,
                                                              ConversationId = p.ConversationId,
                                                              IsAdmin = p.IsAdmin,
                                                              JoinedAt = p.JoinedAt,
                                                              UserId = p.UserId
                                                          }).ToList()
                                  }).FirstAsync();
        return conversation;
    }

    public async Task<ChatThreadSyncFormBusinessObject[]> GetItemsAsync()
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var conversationIds = (from c in _context.Conversations
                             from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                             where p.UserId == userId
                             select c.Id).ToList();

        var conversations = await (from c in _context.Conversations
                                   where conversationIds.Contains(c.Id)
                                   select new ChatThreadSyncFormBusinessObject()
                                   {
                                       Id = c.Id,
                                       CreatedAt = c.CreatedAt,
                                       IsDeleted = c.IsDeleted,
                                       Title = c.Title,
                                       Type = c.Type,
                                       ChatParticipents = (from p in _context.ConversationParticipants
                                                           where p.ConversationId == c.Id
                                                           select new ChatParticipentsSyncFormBusinessObject()
                                                           {
                                                               Id = p.Id,
                                                               ConversationId = p.ConversationId,
                                                               IsAdmin = p.IsAdmin,
                                                               JoinedAt = p.JoinedAt,
                                                               UserId = p.UserId
                                                           }).ToList()
                                   }).ToArrayAsync();
        return conversations;

    }
}
