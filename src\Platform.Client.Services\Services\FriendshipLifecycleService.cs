using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using DeepMessage.MauiApp.Services;
using DeepMessage.Client.Common.Data;
using Microsoft.EntityFrameworkCore;
using Platform.Framework.Core;

namespace Platform.Client.Services.Services;

/// <summary>
/// Interface for friendship lifecycle management
/// Handles friendship state changes and integrates with AES key management
/// </summary>
public interface IFriendshipLifecycleService
{
    /// <summary>
    /// Blocks a friendship and invalidates AES keys
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    Task BlockFriendshipAsync(string friendshipId);

    /// <summary>
    /// Unblocks a friendship and regenerates AES keys
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    Task UnblockFriendshipAsync(string friendshipId);

    /// <summary>
    /// Deletes a friendship and clears AES keys
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    Task DeleteFriendshipAsync(string friendshipId);

    /// <summary>
    /// Restores a deleted friendship and regenerates AES keys
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    Task RestoreFriendshipAsync(string friendshipId);

    /// <summary>
    /// Rotates AES keys for a friendship (security maintenance)
    /// </summary>
    /// <param name="friendshipId">The friendship ID</param>
    Task RotateAESKeysAsync(string friendshipId);
}

/// <summary>
/// Friendship lifecycle service implementation
/// Manages friendship state changes and coordinates with AES key management
/// </summary>
public class FriendshipLifecycleService : IFriendshipLifecycleService
{
    private readonly AppDbContext _context;
    private readonly ILocalStorageService _localStorageService;
    private readonly IAESKeyManagerService _aesKeyManagerService;
    private readonly ILogger<FriendshipLifecycleService> _logger;

    public FriendshipLifecycleService(
        AppDbContext context,
        ILocalStorageService localStorageService,
        IAESKeyManagerService aesKeyManagerService,
        ILogger<FriendshipLifecycleService> logger)
    {
        _context = context;
        _localStorageService = localStorageService;
        _aesKeyManagerService = aesKeyManagerService;
        _logger = logger;
    }

    public async Task BlockFriendshipAsync(string friendshipId)
    {
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            // Update friendship status
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => f.Id == friendshipId && f.UserId == userId);

            if (friendship == null)
            {
                throw new InvalidOperationException($"Friendship not found: {friendshipId}");
            }

            friendship.IsBlocked = true;
            friendship.BlockedAt = DateTime.UtcNow;

            // Invalidate AES keys for security
            await _aesKeyManagerService.InvalidateAESKeysAsync(friendshipId);

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Blocked friendship {FriendshipId} and invalidated AES keys", friendshipId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error blocking friendship {FriendshipId}", friendshipId);
            throw;
        }
    }

    public async Task UnblockFriendshipAsync(string friendshipId)
    {
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            // Update friendship status
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => f.Id == friendshipId && f.UserId == userId);

            if (friendship == null)
            {
                throw new InvalidOperationException($"Friendship not found: {friendshipId}");
            }

            friendship.IsBlocked = false;
            friendship.BlockedAt = null;

            // Regenerate AES keys for fresh security
            await _aesKeyManagerService.RegenerateAESKeysAsync(friendshipId, userId, friendship.FriendId);

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Unblocked friendship {FriendshipId} and regenerated AES keys", friendshipId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unblocking friendship {FriendshipId}", friendshipId);
            throw;
        }
    }

    public async Task DeleteFriendshipAsync(string friendshipId)
    {
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            // Soft delete friendship
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => f.Id == friendshipId && f.UserId == userId);

            if (friendship == null)
            {
                throw new InvalidOperationException($"Friendship not found: {friendshipId}");
            }

            friendship.IsDeleted = true;
            friendship.DeletedAt = DateTime.UtcNow;

            // Invalidate AES keys
            await _aesKeyManagerService.InvalidateAESKeysAsync(friendshipId);

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Deleted friendship {FriendshipId} and invalidated AES keys", friendshipId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting friendship {FriendshipId}", friendshipId);
            throw;
        }
    }

    public async Task RestoreFriendshipAsync(string friendshipId)
    {
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            // Restore friendship
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => f.Id == friendshipId && f.UserId == userId);

            if (friendship == null)
            {
                throw new InvalidOperationException($"Friendship not found: {friendshipId}");
            }

            friendship.IsDeleted = false;
            friendship.DeletedAt = null;

            // Regenerate AES keys for fresh security
            await _aesKeyManagerService.RegenerateAESKeysAsync(friendshipId, userId, friendship.FriendId);

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Restored friendship {FriendshipId} and regenerated AES keys", friendshipId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring friendship {FriendshipId}", friendshipId);
            throw;
        }
    }

    public async Task RotateAESKeysAsync(string friendshipId)
    {
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => f.Id == friendshipId && f.UserId == userId);

            if (friendship == null)
            {
                throw new InvalidOperationException($"Friendship not found: {friendshipId}");
            }

            if (friendship.IsBlocked || friendship.IsDeleted)
            {
                throw new InvalidOperationException("Cannot rotate keys for blocked or deleted friendship");
            }

            // Regenerate AES keys
            await _aesKeyManagerService.RegenerateAESKeysAsync(friendshipId, userId, friendship.FriendId);

            _logger.LogInformation("Rotated AES keys for friendship {FriendshipId}", friendshipId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rotating AES keys for friendship {FriendshipId}", friendshipId);
            throw;
        }
    }
}
