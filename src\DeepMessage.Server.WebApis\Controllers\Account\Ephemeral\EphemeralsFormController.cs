﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
namespace DeepMessage.Server.WebApis.Controller.Account;
[ApiController, Route("api/[controller]/[action]")]
public class EphemeralsFormController : ControllerBase, IEphemeralFormDataService
{

	private readonly IEphemeralFormDataService dataService;

	public EphemeralsFormController(IEphemeralFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] EphemeralFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<EphemeralFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
