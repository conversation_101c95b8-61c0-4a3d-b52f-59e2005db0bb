﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using Platform.Client.Common.Features.AuthCodes;
using Platform.Client.Services.Features.AuthCodes;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
namespace Platform.Client.Common.Features.AuthCodes;
public class AuthCodeListingViewBase : ListingBaseMaui<AuthCodeListingViewModel, AuthCodeListingBusinessObject,
                AuthCodeFilterViewModel, AuthCodeFilterBusinessObject, IAuthCodeListingDataService>
{
    private ICommand? _syncDownItemsCommand;
    public ICommand? SyncDownItemsCommand
    {
        get
        {
            return _syncDownItemsCommand = _syncDownItemsCommand ?? new Command(() =>
            {
                Navigation.PushModalAsync(new AuthCodeFormView(ScopeFactory, string.Empty), false);
            });
        }
    }

    public AuthCodeListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}


public partial class AuthCodeListingView : AuthCodeListingViewBase
{
    public AuthCodeListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        InitializeComponent();
        BindingContext = this;

        // ✅ IMPROVED UX: Removed Share and Copy commands from listing
        // These actions are now available in the generation success modal
    }
}


