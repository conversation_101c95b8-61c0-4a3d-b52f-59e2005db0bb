using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Platform.Client.Services.Services
{
    public interface IClientEncryptionService
    {
        RSAKeyPair GenerateRSAKeyPairAsync();
        byte[] DeriveAESKeyFromPasswordAsync(string password, byte[]? salt = null);
        string EncryptWithAESAsync(string plaintext, byte[] key);
        string DecryptWithAESAsync(string ciphertext, byte[] key);
        string EncryptRSAPrivateKeyAsync(string privateKeyPem, byte[] aesKey);
        string DecryptRSAPrivateKeyAsync(string encryptedPrivateKey, byte[] aesKey);
        byte[] GetSaltFromEncryptedDataAsync(string encryptedData);
        int GenerateBitSignature(string input);

        // RSA Message Encryption Methods for E2E Messaging
        string EncryptWithRSAPublicKey(string plaintext, string publicKeyPem);
        string DecryptWithRSAPrivateKey(string ciphertext, RSA privateKey);

        // AES Key Generation for Ephemeral Encryption
        byte[] GenerateAES256Key();
    }

    public class RSAKeyPair
    {
        public string PublicKeyPem { get; set; } = string.Empty;
        public string PrivateKeyPem { get; set; } = string.Empty;
    }

    public class ClientEncryptionService : IClientEncryptionService
    {
        private const int PBKDF2_ITERATIONS = 100000;
        private const int SALT_SIZE = 32;
        private const int AES_KEY_SIZE = 32; // 256 bits
        private const int IV_SIZE = 16; // 128 bits

        public RSAKeyPair GenerateRSAKeyPairAsync()
        {
            using var rsa = RSA.Create(2048);

            var publicKeyPem = Convert.ToBase64String(rsa.ExportRSAPublicKey());
            var privateKeyPem = Convert.ToBase64String(rsa.ExportRSAPrivateKey());

            return new RSAKeyPair
            {
                PublicKeyPem = publicKeyPem,
                PrivateKeyPem = privateKeyPem
            };
        }

        public byte[] DeriveAESKeyFromPasswordAsync(string password, byte[]? salt = null)
        {
            if (salt == null)
            {
                salt = new byte[SALT_SIZE];
                using var rng = RandomNumberGenerator.Create();
                rng.GetBytes(salt);
            }

            using var pbkdf2 = new Rfc2898DeriveBytes(
                Encoding.UTF8.GetBytes(password),
                salt,
                PBKDF2_ITERATIONS,
                HashAlgorithmName.SHA256);

            return pbkdf2.GetBytes(AES_KEY_SIZE);
        }

        public string EncryptWithAESAsync(string plaintext, byte[] key)
        {
            using var aes = Aes.Create();
            aes.Key = key;
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            var plaintextBytes = Encoding.UTF8.GetBytes(plaintext);
            var ciphertextBytes = encryptor.TransformFinalBlock(plaintextBytes, 0, plaintextBytes.Length);

            // Combine IV + ciphertext
            var result = new byte[IV_SIZE + ciphertextBytes.Length];
            Array.Copy(aes.IV, 0, result, 0, IV_SIZE);
            Array.Copy(ciphertextBytes, 0, result, IV_SIZE, ciphertextBytes.Length);

            return Convert.ToBase64String(result);
        }

        public string DecryptWithAESAsync(string ciphertext, byte[] key)
        {
            var data = Convert.FromBase64String(ciphertext);

            // Extract IV and ciphertext
            var iv = new byte[IV_SIZE];
            var ciphertextBytes = new byte[data.Length - IV_SIZE];
            Array.Copy(data, 0, iv, 0, IV_SIZE);
            Array.Copy(data, IV_SIZE, ciphertextBytes, 0, ciphertextBytes.Length);

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            var plaintextBytes = decryptor.TransformFinalBlock(ciphertextBytes, 0, ciphertextBytes.Length);

            return Encoding.UTF8.GetString(plaintextBytes);
        }

        public string EncryptRSAPrivateKeyAsync(string privateKeyPem, byte[] aesKey)
        {
            return EncryptWithAESAsync(privateKeyPem, aesKey);
        }

        public string DecryptRSAPrivateKeyAsync(string encryptedPrivateKey, byte[] aesKey)
        {
            return DecryptWithAESAsync(encryptedPrivateKey, aesKey);
        }

        public byte[] GetSaltFromEncryptedDataAsync(string encryptedData)
        {
            // For PBKDF2, we'll store salt separately in local storage
            // This method is for future extensibility
            // Return empty array for now since salt is stored separately
            return Array.Empty<byte>();
        }

        public int GenerateBitSignature(string input)
        {
            if (string.IsNullOrEmpty(input) || input.Length < 3)
                throw new ArgumentException("Input must be at least 3 characters long.");

            // Step 1: First 2 characters
            char c1 = input.First();
            char c2 = input.Last();

            int leftBits1 = (c1 >> 6) & 0b11;
            int rightBits1 = c1 & 0b11;

            int leftBits2 = (c2 >> 6) & 0b11;
            int rightBits2 = c2 & 0b11;

            int combined8Bits = (leftBits1 << 6) | (rightBits1 << 4) | (leftBits2 << 2) | rightBits2;

            // Step 2: Middle 4 bits from the middle character
            int middleIndex = input.Length / 2;
            char cm = input[middleIndex];
            int middleBits = (cm >> 2) & 0b1111; // Extract bits 2-5

            // Final 12-bit signature
            int finalSignature = (combined8Bits << 4) | middleBits;

            return finalSignature; // Range: 0 to 4095
        }

        /// <summary>
        /// Encrypts plaintext using RSA public key for end-to-end message encryption
        /// </summary>
        /// <param name="plaintext">The message content to encrypt</param>
        /// <param name="publicKeyPem">Base64-encoded RSA public key</param>
        /// <returns>Base64-encoded encrypted message</returns>
        public string EncryptWithRSAPublicKey(string plaintext, string publicKeyPem)
        {
            if (string.IsNullOrEmpty(plaintext))
                throw new ArgumentException("Plaintext cannot be null or empty.", nameof(plaintext));

            if (string.IsNullOrEmpty(publicKeyPem))
                throw new ArgumentException("Public key cannot be null or empty.", nameof(publicKeyPem));

            try
            {
                using var rsa = RSA.Create();
                var publicKeyBytes = Convert.FromBase64String(publicKeyPem);
                rsa.ImportRSAPublicKey(publicKeyBytes, out _);

                var plaintextBytes = Encoding.UTF8.GetBytes(plaintext);
                var encryptedBytes = rsa.Encrypt(plaintextBytes, RSAEncryptionPadding.OaepSHA256);

                return Convert.ToBase64String(encryptedBytes);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to encrypt message with RSA public key: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Decrypts ciphertext using RSA private key for end-to-end message decryption
        /// </summary>
        /// <param name="ciphertext">Base64-encoded encrypted message</param>
        /// <param name="privateKey">RSA private key instance</param>
        /// <returns>Decrypted plaintext message</returns>
        public string DecryptWithRSAPrivateKey(string ciphertext, RSA privateKey)
        {
            if (string.IsNullOrEmpty(ciphertext))
                throw new ArgumentException("Ciphertext cannot be null or empty.", nameof(ciphertext));

            if (privateKey == null)
                throw new ArgumentNullException(nameof(privateKey));

            try
            {
                var encryptedBytes = Convert.FromBase64String(ciphertext);
                var decryptedBytes = privateKey.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);

                return Encoding.UTF8.GetString(decryptedBytes);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to decrypt message with RSA private key: {ex.Message}", ex);
            }
        }

        public byte[] GenerateAES256Key()
        {
            var key = new byte[AES_KEY_SIZE]; // 32 bytes = 256 bits
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(key);
            }
            return key;
        }
    }

    public class EncryptedCredentials
    {
        public string EncryptedUsername { get; set; } = string.Empty;
        public byte[] Salt { get; set; } = Array.Empty<byte>();
        public string UserId { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
    }
}
