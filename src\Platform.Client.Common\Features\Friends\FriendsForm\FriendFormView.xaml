﻿<?xml version="1.0" encoding="utf-8" ?>
<local:FriendFormViewBase
    x:Class="Platform.Client.Common.Features.Friends.FriendFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    Title="Add Friend"
    x:DataType="local:FriendFormViewBase"
    Background="{StaticResource OverlayColor}"
    IsBusy="True">

    <Grid Padding="16" RowDefinitions="1*, Auto, 2*">
        <Border
            x:Name="MainBorder"
            Grid.Row="1"
            Background="{StaticResource CardBackgroundColor}"
            StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="8" />
            </Border.StrokeShape>

            <ScrollView>
                <Grid RowDefinitions="Auto, Auto, Auto">

                    <!--  Header Section with Icon  -->
                    <VerticalStackLayout
                        Margin="32,32,32,0"
                        HorizontalOptions="Center"
                        Spacing="12">
                        <!--  Header Icon  -->
                        <Border
                            Background="{AppThemeBinding Light={StaticResource Gray100},
                                                         Dark={StaticResource Gray600}}"
                            HeightRequest="64"
                            HorizontalOptions="Center"
                            Stroke="{AppThemeBinding Light={StaticResource Gray400},
                                                     Dark={StaticResource Gray500}}"
                            WidthRequest="64">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="32" />
                            </Border.StrokeShape>
                            <Image
                                HeightRequest="32"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <Image.Source>
                                    <FontImageSource
                                        FontFamily="Jelly"
                                        Glyph="&#xf118;"
                                        Size="16"
                                        Color="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />
                                </Image.Source>
                            </Image>
                        </Border>

                        <!--  Header Text  -->
                        <VerticalStackLayout HorizontalOptions="Center" Spacing="8">
                            <Label
                                FontAttributes="Bold"
                                FontSize="24"
                                HorizontalTextAlignment="Center"
                                Text="Add Friend"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                            <Label
                                Margin="8,0"
                                FontSize="14"
                                HorizontalTextAlignment="Center"
                                LineBreakMode="WordWrap"
                                MaxLines="2"
                                Text="Enter your friend's invitation code to connect"
                                TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                            Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>
                    </VerticalStackLayout>

                    <!--  Friend Code Input  -->
                    <VerticalStackLayout
                        Grid.Row="1"
                        Margin="32"
                        Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Friend Invitation Code"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />

                        <Entry
                            BackgroundColor="Transparent"
                            FontSize="16"
                            Placeholder="Enter invitation code..."
                            PlaceholderColor="{AppThemeBinding Light={StaticResource Gray500},
                                                               Dark={StaticResource Gray500}}"
                            Text="{Binding SelectedItem.AuthCode}"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />
                        <Label
                            FontSize="12"
                            Text="Enter the invitation code shared by your friend"
                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                        Dark={StaticResource Gray400}}" />
                    </VerticalStackLayout>

                    <!--  Add Friend Button  -->
                    <Button
                        Grid.Row="2"
                        Margin="32,0,32,32"
                        Command="{Binding SaveCommand}"
                        FontAttributes="Bold"
                        FontSize="16"
                        IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}"
                        Text="{Binding IsWorking, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Adding Friend...|Add Friend'}"
                        TextColor="White" />

                    <!--  Loading Indicator  -->
                    <ActivityIndicator
                        Margin="0,8"
                        IsRunning="{Binding IsWorking}"
                        IsVisible="{Binding IsWorking}"
                        Color="{AppThemeBinding Light=#004f98,
                                                Dark={StaticResource Gray300}}" />


                    <Button
                        Margin="12,4"
                        BackgroundColor="Transparent"
                        Clicked="Button_Clicked"
                        CornerRadius="0"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        WidthRequest="24">
                        <Button.ImageSource>
                            <FontImageSource
                                FontFamily="Jelly"
                                Glyph="&#xf057;"
                                Size="20"
                                Color="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />
                        </Button.ImageSource>
                    </Button>

                </Grid>
            </ScrollView>
        </Border>
    </Grid>
</local:FriendFormViewBase>
