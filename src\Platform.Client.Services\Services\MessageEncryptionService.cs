using Microsoft.Extensions.Logging;
using System.Security.Claims;
using DeepMessage.MauiApp.Services;
using DeepMessage.ServiceContracts.Features.Account;
using System.Security.Cryptography;
using DeepMessage.ServiceContracts.Features.Friends;
using Platform.Framework.Core;
using Microsoft.Extensions.DependencyInjection;

namespace Platform.Client.Services.Services;


/// <summary>
/// Implementation of message encryption service
/// Handles AES key creation and management for message sending
/// </summary>
public class MessageEncryptionService
{
    private readonly IEphemeralFormDataService _ephemeralFormService;
    private readonly IAESKeyCacheService _cacheService;
    private readonly IClientEncryptionService _encryptionService;
    private readonly ISecureKeyManager _secureKeyManager;
    private readonly IFriendsListingDataService _friendsListingService;
    private readonly ILocalStorageService _localStorageService;

    private readonly IServiceScopeFactory serviceScopeFactory;
    private readonly ILogger<MessageEncryptionService> _logger;

    public MessageEncryptionService(
        IEphemeralFormDataService ephemeralFormService,
        IAESKeyCacheService cacheService,
        IClientEncryptionService encryptionService,
        ISecureKeyManager secureKeyManager,
        IFriendsListingDataService friendsListingService,
        ILocalStorageService localStorageService,
        IServiceScopeFactory serviceScopeFactory,
        ILogger<MessageEncryptionService> logger)
    {
        _ephemeralFormService = ephemeralFormService;
        _cacheService = cacheService;
        _encryptionService = encryptionService;
        _secureKeyManager = secureKeyManager;
        _friendsListingService = friendsListingService;
        _localStorageService = localStorageService;
        this.serviceScopeFactory = serviceScopeFactory;
        _logger = logger;
    }

    public async Task<byte[]> GetOrCreateEncryptionKeyAsync(string friendshipId, string senderId, string receiverId)
    {
        _logger.LogDebug("Getting or creating encryption key for friendship {FriendshipId}", friendshipId);

        // Check cache first
        var cachedKey = _cacheService.GetDecryptedAESKeyAsync(friendshipId);
        if (cachedKey != null)
        {
            _logger.LogDebug("Using cached AES key for friendship {FriendshipId}", friendshipId);
            return cachedKey;
        }

        // Try to get from local database
        var friendshipData = await _ephemeralFormService.GetItemByIdAsync(friendshipId);
        if (friendshipData?.SenderAESKey != null)
        {
            var decryptedKey = DecryptAESKey(friendshipData.SenderAESKey);
            if (decryptedKey != null)
            {
                _cacheService.CacheDecryptedAESKeyAsync(friendshipId, decryptedKey);
                _logger.LogDebug("Retrieved existing AES key from local database for friendship {FriendshipId}", friendshipId);
                return decryptedKey;
            }
        }

        // If still not found, generate new key
        _logger.LogInformation("Generating new AES key for friendship {FriendshipId}", friendshipId);

        using var aes = Aes.Create();
        aes.GenerateKey();
        var aesKey = aes.Key;

        // Get public keys for both users
        var senderPublicKey = await GetUserPublicKey(senderId);
        var receiverPublicKey = await GetUserPublicKey(receiverId);

        if (senderPublicKey == null || receiverPublicKey == null)
        {
            throw new InvalidOperationException("Unable to retrieve public keys for AES key encryption");
        }

        // Encrypt AES key with both users' RSA public keys
        var senderEncryptedKey = _encryptionService.EncryptWithRSAPublicKey(Convert.ToBase64String(aesKey), senderPublicKey);
        var receiverEncryptedKey = _encryptionService.EncryptWithRSAPublicKey(Convert.ToBase64String(aesKey), receiverPublicKey);

        // Store encrypted keys locally
        var formData = new EphemeralFormBusinessObject
        {
            FriendshipId = friendshipId,
            SenderAESKey = senderEncryptedKey,
            ReceiverAESKey = receiverEncryptedKey,
            Operation = "create"
        };

        await _ephemeralFormService.SaveAsync(formData);

        var scope = serviceScopeFactory.CreateScope();
        var chatSyncUpService = scope.ServiceProvider.GetRequiredService<ChatSyncUpService>();
        chatSyncUpService.Sync(new ChatSyncItem() { Id = friendshipId, SyncType = SyncType.Ephemeral });

        // Cache the decrypted key
        _cacheService.CacheDecryptedAESKeyAsync(friendshipId, aesKey);
        _logger.LogDebug("Generated and stored new AES key for friendship {FriendshipId}", friendshipId);
        return aesKey;

    }

    public string GenerateFriendshipId(string userId1, string userId2)
    {
        if (string.IsNullOrEmpty(userId1) || string.IsNullOrEmpty(userId2))
            throw new ArgumentException("Both user IDs must be non-null and non-empty");

        if (userId1.Length != userId2.Length)
            throw new ArgumentException("Both user IDs must have the same length");

        string combined = string.Empty;
        for (int i = 0; i < userId1.Length; i++)
        {
            combined += (i % 2 == 0) ? userId1[i] : userId2[i];
        }

        return combined;
    }

    private byte[]? DecryptAESKey(string encryptedKey)
    {
        if (!_secureKeyManager.IsRSAKeyAvailable())
        {
            _logger.LogWarning("RSA key not available for AES key decryption");
            return null;
        }

        try
        {
            using var privateKey = _secureKeyManager.GetRSAPrivateKeyAsync();
            var decryptedKeyBase64 = _encryptionService.DecryptWithRSAPrivateKey(encryptedKey, privateKey);
            return Convert.FromBase64String(decryptedKeyBase64);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt AES key");
            return null;
        }
    }



    private async Task<string?> GetUserPublicKey(string userId)
    {
        var currentUserId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);

        // If this is the current user, get their public key from the secure key manager
        if (userId == currentUserId && _secureKeyManager.IsRSAKeyAvailable())
        {
            using var privateKey = _secureKeyManager.GetRSAPrivateKeyAsync();
            var publicKeyBytes = privateKey.ExportRSAPublicKey();
            return Convert.ToBase64String(publicKeyBytes);
        }

        // For other users, query friends listing to find their public key
        var filter = new FriendsFilterBusinessObject { UsePagination = false };
        var friendsResult = await _friendsListingService.GetPaginatedItems(filter);
        var friend = friendsResult.Items?.FirstOrDefault(f => f.FriendId == userId);

        return friend?.Pub1;
    }
}
