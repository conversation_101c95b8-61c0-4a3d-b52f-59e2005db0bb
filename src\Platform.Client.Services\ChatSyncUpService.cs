﻿using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Client.Common.Data;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using DeepMessage.ServiceContracts.Features.Account;

namespace DeepMessage.MauiApp.Services;

public class ChatSyncUpService
{
    private readonly IServiceScopeFactory scopeFactory;
    private readonly BaseHttpClient httpClient;
    private readonly ILogger<ChatSyncUpService> logger;

    private BlockingCollection<ChatSyncItem> SyncItems { get; set; }

    public void Sync(ChatSyncItem item)
    {
        SyncItems.Add(item);
    }

    public ChatSyncUpService(IServiceScopeFactory scopeFactory,
        BaseHttpClient httpClient,
        ILogger<ChatSyncUpService> logger)
    {
        this.scopeFactory = scopeFactory;
        this.httpClient = httpClient;
        this.logger = logger;
        SyncItems = [
            new ChatSyncItem() { Id = string.Empty, SyncType =  SyncType.ChatThread },
            new ChatSyncItem() { Id = string.Empty, SyncType = SyncType.ChatMessage },
            new ChatSyncItem() { Id = string.Empty, SyncType =  SyncType.ReadReceiptAcknowledgment } // Read receipt acknowledgments
        ];
    }
    private bool _keepRunning = true;

    private Task? _syncTask;
    private Task? _monitorTask;
    public void Start()
    {
        if (_monitorTask != null && _monitorTask.Status == TaskStatus.Running)
            return;

        _keepRunning = true;

        _syncTask = Task.Factory.StartNew(async () =>
        {
            while (_keepRunning)
            {
                var item = SyncItems.Take();
                try
                {

                    using var scope = scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    if (item.SyncType == SyncType.ChatThread)
                    {

                        var conversations = await (from c in context.Conversations
                                                   where c.SyncStatus == 0
                                                   select new ChatThreadSyncFormBusinessObject
                                                   {
                                                       Id = c.Id,
                                                       CreatedAt = c.CreatedAt,
                                                       IsDeleted = c.IsDeleted,
                                                       Title = c.Title,
                                                       Type = c.Type,
                                                       ChatParticipents = (from p in context.ConversationParticipants
                                                                           where c.Id == p.ConversationId
                                                                           select new ChatParticipentsSyncFormBusinessObject()
                                                                           {
                                                                               Id = p.Id,
                                                                               ConversationId = p.ConversationId,
                                                                               IsAdmin = p.IsAdmin,
                                                                               JoinedAt = p.JoinedAt,
                                                                               UserId = p.UserId,
                                                                           }).ToList()
                                                   }).ToArrayAsync();

                        foreach (var conversation in conversations)
                        {
                            var chatThreadSyncService = scope.ServiceProvider.GetRequiredService<IChatThreadSyncFormDataService>();
                            await chatThreadSyncService.SaveAsync(conversation);
                            context.Conversations.Where(x => x.Id == conversation.Id)
                                .ExecuteUpdate(x => x.SetProperty(p => p.SyncStatus, 1));
                        }
                    }
                    if (item.SyncType == SyncType.ChatMessage)
                    {
                        var messages = await (from m in context.Messages
                                              where m.DeliveryStatus <= MessageDeliveryStatus.QueuedToUpSync
                                              select new ChatMessagesSyncFormBusinessObject()
                                              {
                                                  Id = m.Id,
                                                  ConversationId = m.ConversationId,
                                                  CreatedAt = m.CreatedAt,
                                                  DeletedAt = m.DeletedAt,
                                                  DisappearAfter = m.DisappearAfter,
                                                  DisappearAt = m.DisappearAt,
                                                  EditedAt = m.EditedAt,
                                                  IsDeleted = m.IsDeleted,
                                                  IsEdited = m.IsEdited,
                                                  IsEphemeral = m.IsEphemeral,
                                                  SenderId = m.SenderId,
                                                  MessageRecipients = (from mr in context.MessageRecipients
                                                                       where mr.MessageId == m.Id
                                                                       select new MessageRecipientSyncFormBusinessObject
                                                                       {
                                                                           Id = mr.Id,
                                                                           MessageId = mr.MessageId,
                                                                           RecipientId = mr.RecipientId,
                                                                           EncryptedContent = mr.EncryptedContent,
                                                                           DeliveryStatus = mr.MessageDeliveryStatus,
                                                                           DeliveryStatusTime = mr.ReadAt,
                                                                           IsRead = mr.IsRead,
                                                                           ReadAt = mr.ReadAt
                                                                       }).ToList()
                                              }).ToListAsync();
                        logger.LogDebug("SyncUpService: Queued {0} messages to sync", messages.Count);
                        foreach (var message in messages)
                        {
                            try
                            {
                                var chatMessagesSyncService = scope.ServiceProvider.GetRequiredService<IChatMessagesSyncFormDataService>();
                                await chatMessagesSyncService.SaveAsync(message);

                                await context.Messages.Where(x => x.Id == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, MessageDeliveryStatus.SentToMessageServer));

                                await context.MessageRecipients.Where(x => x.MessageId == message.Id && x.RecipientId != message.SenderId)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.MessageDeliveryStatus, MessageDeliveryStatus.SentToMessageServer));

                                logger.LogDebug("Successfully synced message {MessageId}", message.Id);
                            }

                            catch (Exception ex)
                            {
                                logger.LogWarning(ex, "error syncing message {MessageId}", message.Id);

                                // ✅ SECURE: Mark as failed and retry later for both Message and MessageRecipients
                                await context.Messages.Where(x => x.Id == message.Id)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, MessageDeliveryStatus.Pending));

                                await context.MessageRecipients.Where(x => x.MessageId == message.Id && x.RecipientId != message.SenderId)
                                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.MessageDeliveryStatus, MessageDeliveryStatus.Pending));
                            }
                        }
                    }

                    if (item.SyncType == SyncType.ReadReceiptAcknowledgment)
                    {
                        var messageRecipients = (from r in context.MessageRecipients
                                                 join m in context.Messages on r.MessageId equals m.Id
                                                 where r.DeliveryAcknowledgementStatus == AcknowledgementStatus.QueuedToUpSync
                                                 && m.SenderId != r.RecipientId // compare with current user id
                                                 select new
                                                 {
                                                     m.SenderId,
                                                     r.Id,
                                                     r.MessageId,
                                                     r.ReadAt
                                                 }).ToList();

                        if (messageRecipients.Count == 0)
                        {
                            continue;
                        }
                        var signalRService = scope.ServiceProvider.GetRequiredService<SignalRClientService>();
                        foreach (var receipt in messageRecipients)
                        {
                            await signalRService.AcknowledgeMessageRead(receipt.MessageId, receipt.SenderId, receipt.ReadAt.GetValueOrDefault());
                            var effectedRows = await context.MessageRecipients
                                  .Where(x => x.Id == receipt.Id)
                                  .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryAcknowledgementStatus, AcknowledgementStatus.SentToMessageServer));
                            Console.WriteLine($"Acknowledgment for message {receipt.MessageId} sent to {receipt.SenderId}, affected rows: {effectedRows}");
                        }
                        // Attempt to send acknowledgment via SignalR

                    }

                    if (item.SyncType == SyncType.Ephemeral)
                    {
                        var friendships = await (from f in context.Friendships
                                                 where f.SyncStatus == 1
                                                 select new EphemeralFormBusinessObject()
                                                 {
                                                     FriendshipId = f.Id,
                                                     SenderAESKey = f.SenderAESKey,
                                                     ReceiverAESKey = f.ReceiverAESKey,
                                                 }).ToListAsync();
                        foreach (var friendship in friendships)
                        {
                            var ephemeralService = scope.ServiceProvider.GetRequiredKeyedService<IEphemeralFormDataService>("client");
                            await ephemeralService.SaveAsync(friendship);
                            context.Friendships.Where(x => x.Id == friendship.FriendshipId)
                                .ExecuteUpdate(x => x.SetProperty(p => p.SyncStatus, 2));
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex.Message);
                }
            }
        });


        _monitorTask = Task.Factory.StartNew(async () =>
        {
            while (_keepRunning)
            {
                try
                {
                    using var scope = scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                    await (from f in context.Friendships
                           where f.SyncStatus == 1
                           select new ChatSyncItem()
                           {
                               Id = f.Id,
                               SyncType = SyncType.Ephemeral
                           }).ForEachAsync((x) => Sync(x));


                    await (from m in context.Messages
                           where m.DeliveryStatus == MessageDeliveryStatus.QueuedToUpSync
                           select new ChatSyncItem()
                           {
                               Id = m.Id,
                               SyncType = SyncType.ChatMessage
                           }).ForEachAsync((x) => Sync(x));


                    await (from x in context.MessageRecipients
                           where x.DeliveryAcknowledgementStatus <= AcknowledgementStatus.QueuedToUpSync
                           select new ChatSyncItem()
                           {
                               Id = x.Id,
                               SyncType = SyncType.ReadReceiptAcknowledgment
                           }).ForEachAsync((x) => Sync(x));
                            
                    await Task.Delay(30000);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex.Message);
                    await Task.Delay(60000);
                }
            }
        });
    }

    public void Stop()
    {
        _keepRunning = false;
    }
}


public class ChatSyncItem
{
    public string Id { get; set; } = null!;

    public SyncType SyncType { get; set; }

}

public enum SyncType
{
    ChatThread = 0,
    ChatMessage = 1,
    ReadReceiptAcknowledgment = 2,
    Ephemeral = 3
}